{"higher_level": [], "index": "forcecage", "name": "Forcecage", "desc": ["An immobile, invisible, cube-shaped prison composed of magical force springs into existence around an area you choose within range. The prison can be a cage or a solid box, as you choose.", "A prison in the shape of a cage can be up to 20 feet on a side and is made from 1/2-inch diameter bars spaced 1/2 inch apart.", "A prison in the shape of a box can be up to 10 feet on a side, creating a solid barrier that prevents any matter from passing through it and blocking any spells cast into or out from the area.", "When you cast the spell, any creature that is completely inside the cage's area is trapped. Creatures only partially within the area, or those too large to fit inside the area, are pushed away from the center of the area until they are completely outside the area.", "A creature inside the cage can't leave it by nonmagical means. If the creature tries to use teleportation or interplanar travel to leave the cage, it must first make a charisma saving throw. On a success, the creature can use that magic to exit the cage. On a failure, the creature can't exit the cage and wastes the use of the spell or effect. The cage also extends into the Ethereal Plane, blocking ethereal travel.", "This spell can't be dispelled by dispel magic."], "range": "100 feet", "components": ["V", "S", "M"], "material": "Ruby dust worth 1,500 gp.", "ritual": false, "duration": "1 hour", "concentration": false, "casting_time": "1 action", "level": 7, "area_of_effect": {"type": "cube", "size": 20}, "school": {"index": "evocation", "name": "Evocation", "url": "/api/2014/magic-schools/evocation"}, "classes": [{"index": "bard", "name": "Bard", "url": "/api/2014/classes/bard"}, {"index": "warlock", "name": "<PERSON><PERSON>", "url": "/api/2014/classes/warlock"}, {"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [], "url": "/api/2014/spells/forcecage", "updated_at": "2025-04-08T21:14:16.147Z"}