{"index": "dominate-beast", "name": "Dominate Beast", "desc": ["You attempt to beguile a creature that you can see within range. It must succeed on a wisdom saving throw or be charmed by you for the duration. If you or creatures that are friendly to you are fighting it, it has advantage on the saving throw.", "While the creature is charmed, you have a telepathic link with it as long as the two of you are on the same plane of existence. You can use this telepathic link to issue commands to the creature while you are conscious (no action required), which it does its best to obey. You can specify a simple and general course of action, such as \"Attack that creature,\" \"Run over there,\" or \"Fetch that object.\" If the creature completes the order and doesn't receive further direction from you, it defends and preserves itself to the best of its ability.", "You can use your action to take total and precise control of the target. Until the end of your next turn, the creature takes only the actions you choose, and doesn't do anything that you don't allow it to do. During this time, you can also cause the creature to use a reaction, but this requires you to use your own reaction as well. Each time the target takes damage, it makes a new wisdom saving throw against the spell. If the saving throw succeeds, the spell ends."], "higher_level": ["When you cast this spell with a 9th level spell slot, the duration is concentration, up to 8 hours."], "range": "60 feet", "components": ["V", "S"], "ritual": false, "duration": "Up to 1 minute", "concentration": true, "casting_time": "1 action", "level": 4, "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_success": "none"}, "school": {"index": "enchantment", "name": "Enchantment", "url": "/api/2014/magic-schools/enchantment"}, "classes": [{"index": "druid", "name": "Druid", "url": "/api/2014/classes/druid"}, {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}], "subclasses": [], "url": "/api/2014/spells/dominate-beast", "updated_at": "2025-04-08T21:14:16.147Z"}