import 'constants.dart';

/// API配置类
class ApiConfig {
  static const String baseUrl = 'http://localhost:8092/api';  // 修正端口号
  
  // 认证相关API
  static const String loginEndpoint = '$baseUrl/auth/login';
  
  // 角色相关API
  static const String createCharacterEndpoint = '$baseUrl/character/create';
  static const String deleteCharacterEndpoint = '$baseUrl/character/delete';
  static String getCharacterListEndpoint(String username) => '$baseUrl/character/list/$username';
  static String getCharacterDetailEndpoint(String characterId) => '$baseUrl/character/detail/$characterId';
  
  // 副本相关API
  static const String dungeonListEndpoint = '$baseUrl/dungeon/list';
  static String dungeonDetailEndpoint(String dungeonId) => '$baseUrl/dungeon/detail/$dungeonId';
  static const String enterDungeonEndpoint = '$baseUrl/dungeon/enter';
  static const String dungeonCommandEndpoint = '$baseUrl/dungeon/command';
  
  // 武器商店相关API
  static const String weaponListEndpoint = '$baseUrl/weaponShop/list';
  static const String buyWeaponEndpoint = '$baseUrl/weaponShop/buy';
  static const String sellWeaponEndpoint = '$baseUrl/weaponShop/sell';
  
  // 图像生成相关API
  static const String generateNpcImageEndpoint = '$baseUrl/generate/npc_image';
  
  // HTTP请求头
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  // 请求超时时间（秒）
  static const int timeoutSeconds = 30;
}
