import 'package:flutter/foundation.dart';

/// 调试日志工具类
class DebugLogger {
  static const String _tag = '[DND_DEBUG]';
  
  /// 信息日志
  static void info(String message, {String? tag}) {
    if (kDebugMode) {
      print('$_tag ${tag ?? 'INFO'}: $message');
    }
  }
  
  /// 错误日志
  static void error(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      print('$_tag ${tag ?? 'ERROR'}: $message');
      if (error != null) {
        print('$_tag ERROR_DETAIL: $error');
      }
      if (stackTrace != null) {
        print('$_tag STACK_TRACE: $stackTrace');
      }
    }
  }
  
  /// 警告日志
  static void warning(String message, {String? tag}) {
    if (kDebugMode) {
      print('$_tag ${tag ?? 'WARNING'}: $message');
    }
  }
  
  /// 调试日志
  static void debug(String message, {String? tag}) {
    if (kDebugMode) {
      print('$_tag ${tag ?? 'DEBUG'}: $message');
    }
  }
  
  /// API请求日志
  static void apiRequest(String method, String url, {Map<String, dynamic>? data}) {
    if (kDebugMode) {
      print('$_tag API_REQUEST: $method $url');
      if (data != null) {
        print('$_tag API_REQUEST_DATA: $data');
      }
    }
  }
  
  /// API响应日志
  static void apiResponse(String url, int statusCode, {dynamic data}) {
    if (kDebugMode) {
      print('$_tag API_RESPONSE: $url - Status: $statusCode');
      if (data != null) {
        print('$_tag API_RESPONSE_DATA: $data');
      }
    }
  }
  
  /// 页面导航日志
  static void navigation(String from, String to) {
    if (kDebugMode) {
      print('$_tag NAVIGATION: $from -> $to');
    }
  }
  
  /// 用户操作日志
  static void userAction(String action, {String? tag, Map<String, dynamic>? params}) {
    if (kDebugMode) {
      print('$_tag ${tag ?? 'USER_ACTION'}: $action');
      if (params != null) {
        print('$_tag ACTION_PARAMS: $params');
      }
    }
  }
  
  /// 状态变化日志
  static void stateChange(String component, String from, String to) {
    if (kDebugMode) {
      print('$_tag STATE_CHANGE: $component - $from -> $to');
    }
  }
  
  /// 网络连接日志
  static void network(String message, {bool isError = false}) {
    if (kDebugMode) {
      final level = isError ? 'NETWORK_ERROR' : 'NETWORK';
      print('$_tag $level: $message');
    }
  }
}
