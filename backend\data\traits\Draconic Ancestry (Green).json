{"index": "draconic-ancestry-green", "races": [{"index": "dragonborn", "name": "Dragonborn", "url": "/api/2014/races/dragonborn"}], "subraces": [], "name": "Draconic Ancestry (Green)", "desc": ["You have draconic ancestry. Choose one type of dragon from the Draconic Ancestry table. Your breath weapon and damage resistance are determined by the dragon type, as shown in the table."], "parent": {"index": "draconic-ancestry", "name": "Draconic Ancestry", "url": "/api/2014/traits/draconic-ancestry"}, "proficiencies": [], "trait_specific": {"damage_type": {"index": "poison", "name": "Poison", "url": "/api/2014/damage-types/poison"}, "breath_weapon": {"name": "Breath Weapon", "desc": ["You can use your action to exhale destructive energy. Your draconic ancestry determines the size, shape, and damage type of the exhalation. When you use your breath weapon, each creature in the area of the exhalation must make a saving throw, the type of which is determined by your draconic ancestry. The DC for this saving throw equals 8 + your Constitution modifier + your proficiency bonus. A creature takes 2d6 damage on a failed save, and half as much damage on a successful one. The damage increases to 3d6 at 6th level, 4d6 at 11th level, and 5d6 at 16th level. After you use your breath weapon, you can't use it again until you complete a short or long rest."], "area_of_effect": {"size": 15, "type": "cone"}, "usage": {"type": "per rest", "times": 1}, "dc": {"dc_type": {"index": "con", "name": "CON", "url": "/api/2014/ability-scores/con"}, "success_type": "half"}, "damage": [{"damage_type": {"index": "poison", "name": "Poison", "url": "/api/2014/damage-types/poison"}, "damage_at_character_level": {"1": "2d6", "6": "3d6", "11": "4d6", "16": "5d6"}}]}}, "url": "/api/2014/traits/draconic-ancestry-green", "updated_at": "2025-04-08T21:14:18.347Z"}