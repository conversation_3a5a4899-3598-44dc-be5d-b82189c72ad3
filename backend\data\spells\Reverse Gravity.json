{"higher_level": [], "index": "reverse-gravity", "name": "Reverse Gravity", "desc": ["This spell reverses gravity in a 50-foot-radius, 100-foot high cylinder centered on a point within range. All creatures and objects that aren't somehow anchored to the ground in the area fall upward and reach the top of the area when you cast this spell. A creature can make a dexterity saving throw to grab onto a fixed object it can reach, thus avoiding the fall.", "If some solid object (such as a ceiling) is encountered in this fall, falling objects and creatures strike it just as they would during a normal downward fall. If an object or creature reaches the top of the area without striking anything, it remains there, oscillating slightly, for the duration.", "At the end of the duration, affected objects and creatures fall back down."], "range": "100 feet", "components": ["V", "S", "M"], "material": "A lodestone and iron filings.", "ritual": false, "duration": "Up to 1 minute", "concentration": true, "casting_time": "1 action", "level": 7, "dc": {"dc_type": {"index": "dex", "name": "DEX", "url": "/api/2014/ability-scores/dex"}, "dc_success": "other", "desc": "A creature can make a dexterity saving throw to grab onto a fixed object it can reach, thus avoiding the fall."}, "area_of_effect": {"type": "cylinder", "size": 50}, "school": {"index": "transmutation", "name": "Transmutation", "url": "/api/2014/magic-schools/transmutation"}, "classes": [{"index": "druid", "name": "Druid", "url": "/api/2014/classes/druid"}, {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}, {"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [], "url": "/api/2014/spells/reverse-gravity", "updated_at": "2025-04-08T21:14:16.147Z"}