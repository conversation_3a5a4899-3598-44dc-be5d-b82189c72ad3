{"index": "warlock", "name": "<PERSON><PERSON>", "hit_die": 8, "proficiency_choices": [{"desc": "Choose two skills from <PERSON><PERSON>, Deception, History, Intimidation, Investigation, Nature, and Religion", "choose": 2, "type": "proficiencies", "from": {"option_set_type": "options_array", "options": [{"option_type": "reference", "item": {"index": "skill-arcana", "name": "Skill: <PERSON>ana", "url": "/api/2014/proficiencies/skill-arcana"}}, {"option_type": "reference", "item": {"index": "skill-deception", "name": "Skill: Deception", "url": "/api/2014/proficiencies/skill-deception"}}, {"option_type": "reference", "item": {"index": "skill-history", "name": "Skill: History", "url": "/api/2014/proficiencies/skill-history"}}, {"option_type": "reference", "item": {"index": "skill-intimidation", "name": "Skill: Intimidation", "url": "/api/2014/proficiencies/skill-intimidation"}}, {"option_type": "reference", "item": {"index": "skill-investigation", "name": "Skill: Investigation", "url": "/api/2014/proficiencies/skill-investigation"}}, {"option_type": "reference", "item": {"index": "skill-nature", "name": "Skill: Nature", "url": "/api/2014/proficiencies/skill-nature"}}, {"option_type": "reference", "item": {"index": "skill-religion", "name": "Skill: Religion", "url": "/api/2014/proficiencies/skill-religion"}}]}}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/proficiencies/simple-weapons"}, {"index": "saving-throw-wis", "name": "Saving Throw: WIS", "url": "/api/2014/proficiencies/saving-throw-wis"}, {"index": "saving-throw-cha", "name": "Saving Throw: CHA", "url": "/api/2014/proficiencies/saving-throw-cha"}], "saving_throws": [{"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, {"index": "cha", "name": "CHA", "url": "/api/2014/ability-scores/cha"}], "starting_equipment": [{"equipment": {"index": "dagger", "name": "<PERSON>gger", "url": "/api/2014/equipment/dagger"}, "quantity": 2}, {"equipment": {"index": "leather-armor", "name": "<PERSON><PERSON> Armor", "url": "/api/2014/equipment/leather-armor"}, "quantity": 1}], "starting_equipment_options": [{"desc": "(a) a light crossbow and 20 bolts or (b) any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "multiple", "items": [{"option_type": "counted_reference", "count": 1, "of": {"index": "crossbow-light", "name": "Crossbow, light", "url": "/api/2014/equipment/crossbow-light"}}, {"option_type": "counted_reference", "count": 20, "of": {"index": "crossbow-bolt", "name": "Crossbow bolt", "url": "/api/2014/equipment/crossbow-bolt"}}]}, {"option_type": "choice", "choice": {"desc": "any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/equipment-categories/simple-weapons"}}}}]}}, {"desc": "(a) a component pouch or (b) an arcane focus", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "component-pouch", "name": "Component pouch", "url": "/api/2014/equipment/component-pouch"}}, {"option_type": "choice", "choice": {"desc": "arcane focus", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "arcane-foci", "name": "<PERSON><PERSON>", "url": "/api/2014/equipment-categories/arcane-foci"}}}}]}}, {"desc": "(a) a scholar’s pack or (b) a dungeoneer’s pack", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "scholars-pack", "name": "Scholar's Pack", "url": "/api/2014/equipment/scholars-pack"}}, {"option_type": "counted_reference", "count": 1, "of": {"index": "dungeoneers-pack", "name": "Dungeoneer's Pack", "url": "/api/2014/equipment/dungeoneers-pack"}}]}}, {"desc": "any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/equipment-categories/simple-weapons"}}}], "class_levels": "/api/2014/classes/warlock/levels", "multi_classing": {"prerequisites": [{"ability_score": {"index": "cha", "name": "CHA", "url": "/api/2014/ability-scores/cha"}, "minimum_score": 13}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/proficiencies/simple-weapons"}]}, "subclasses": [{"index": "fiend", "name": "Fiend", "url": "/api/2014/subclasses/fiend"}], "spellcasting": {"level": 1, "spellcasting_ability": {"index": "cha", "name": "CHA", "url": "/api/2014/ability-scores/cha"}, "info": [{"name": "Cantrips", "desc": ["You know two cantrips of your choice from the warlock spell list. You learn additional warlock cantrips of your choice at higher levels, as shown in the Cantrips Known column of the Warlock table."]}, {"name": "Spell Slots", "desc": ["The Warlock table shows how many spell slots you have. The table also shows what the level of those slots is; all of your spell slots are the same level. To cast one of your warlock spells of 1st level or higher, you must expend a spell slot. You regain all expended spell slots when you finish a short or long rest.", "For example, when you are 5th level, you have two 3rd-level spell slots. To cast the 1st-level spell thunderwave, you must spend one of those slots, and you cast it as a 3rd-level spell."]}, {"name": "Spells Known of 1st Level and Higher", "desc": ["At 1st level, you know two 1st-level spells of your choice from the warlock spell list.", "The Spells Known column of the Warlock table shows when you learn more warlock spells of your choice of 1st level and higher. ", "A spell you choose must be of a level no higher than what's shown in the table's Slot Level column for your level. When you reach 6th level, for example, you learn a new warlock spell, which can be 1st, 2nd, or 3rd level.", "Additionally, when you gain a level in this class, you can choose one of the warlock spells you know and replace it with another spell from the warlock spell list, which also must be of a level for which you have spell slots."]}, {"name": "Spellcasting Ability", "desc": ["Charisma is your spellcasting ability for your warlock spells, so you use your Charisma whenever a spell refers to your spellcasting ability. In addition, you use your Charisma modifier when setting the saving throw DC for a warlock spell you cast and when making an attack roll with one.", "Spell save DC = 8 + your proficiency bonus + your Charisma modifier.", "Spell attack modifier = your proficiency bonus + your Charisma modifier."]}, {"name": "Spellcasting Focus", "desc": ["You can use an arcane focus as a spellcasting focus for your warlock spells."]}]}, "spells": "/api/2014/classes/warlock/spells", "url": "/api/2014/classes/warlock", "updated_at": "2025-04-08T21:13:56.155Z"}