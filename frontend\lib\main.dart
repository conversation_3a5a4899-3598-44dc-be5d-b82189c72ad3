import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'providers/auth_provider.dart';
import 'providers/character_provider.dart';
import 'screens/character_selection_screen.dart';
import 'utils/debug_logger.dart';

void main() {
  DebugLogger.info('=== DND游戏Flutter应用启动 ===', tag: 'MAIN');
  runApp(const DndGameApp());
}

class DndGameApp extends StatelessWidget {
  const DndGameApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    DebugLogger.info('构建主应用组件', tag: 'MAIN');
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) {
          DebugLogger.debug('创建AuthProvider', tag: 'PROVIDER');
          return AuthProvider();
        }),
        ChangeNotifierProvider(create: (_) {
          DebugLogger.debug('创建CharacterProvider', tag: 'PROVIDER');
          return CharacterProvider();
        }),
      ],
      child: MaterialApp(
        title: 'DND 游戏',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.deepPurple,
          useMaterial3: true,
        ),
        home: const SimpleLoginScreen(),
      ),
    );
  }
}

class SimpleLoginScreen extends StatefulWidget {
  const SimpleLoginScreen({Key? key}) : super(key: key);

  @override
  State<SimpleLoginScreen> createState() => _SimpleLoginScreenState();
}

class _SimpleLoginScreenState extends State<SimpleLoginScreen> {
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    DebugLogger.info('登录界面初始化', tag: 'LOGIN');
  }

  @override
  void dispose() {
    DebugLogger.debug('登录界面销毁', tag: 'LOGIN');
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() async {
    final username = _usernameController.text.trim();
    final password = _passwordController.text;

    DebugLogger.userAction('点击登录按钮', params: {
      'username': username,
      'password_length': password.length,
    });

    if (username.isEmpty || password.isEmpty) {
      DebugLogger.warning('登录信息不完整', tag: 'LOGIN');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入用户名和密码')),
      );
      return;
    }

    // 显示加载状态
    DebugLogger.debug('显示登录加载状态', tag: 'LOGIN');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('正在登录...')),
    );

    try {
      DebugLogger.debug('获取AuthProvider实例', tag: 'LOGIN');
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      DebugLogger.info('开始执行登录', tag: 'LOGIN');
      final success = await authProvider.login(username, password);

      if (success && mounted) {
        DebugLogger.info('登录成功，跳转到角色选择界面', tag: 'LOGIN');
        DebugLogger.navigation('SimpleLoginScreen', 'CharacterSelectionScreen');

        // 跳转到角色选择界面
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const CharacterSelectionScreen(),
          ),
        );
      } else if (mounted) {
        DebugLogger.error('登录失败: ${authProvider.error}', tag: 'LOGIN');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('登录失败: ${authProvider.error ?? "未知错误"}')),
        );
      }
    } catch (e) {
      DebugLogger.error('登录过程中发生异常', tag: 'LOGIN', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('登录出错: $e')),
        );
      }
    }
  }

  void _testConnection() async {
    DebugLogger.userAction('测试后端连接');

    try {
      DebugLogger.debug('开始测试后端连接', tag: 'CONNECTION_TEST');
      final response = await http.get(
        Uri.parse('http://localhost:8092/api'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      DebugLogger.debug('连接测试响应: ${response.statusCode}', tag: 'CONNECTION_TEST');
      DebugLogger.debug('响应体: ${response.body}', tag: 'CONNECTION_TEST');

      if (response.statusCode == 200) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ 后端连接正常'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ 后端响应异常: ${response.statusCode}'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      DebugLogger.error('连接测试失败', tag: 'CONNECTION_TEST', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ 无法连接后端: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('DND 游戏测试'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.deepPurple.withOpacity(0.8),
              Colors.deepPurple,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Card(
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 游戏标题
                      Text(
                        'DND 游戏',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.deepPurple,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '龙与地下城 RPG 冒险',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 32),

                      // 登录表单
                      TextField(
                        controller: _usernameController,
                        decoration: const InputDecoration(
                          labelText: '用户名',
                          prefixIcon: Icon(Icons.person),
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),

                      TextField(
                        controller: _passwordController,
                        decoration: const InputDecoration(
                          labelText: '密码',
                          prefixIcon: Icon(Icons.lock),
                          border: OutlineInputBorder(),
                        ),
                        obscureText: true,
                        onSubmitted: (_) => _handleLogin(),
                      ),
                      const SizedBox(height: 24),

                      // 登录按钮
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          return SizedBox(
                            width: double.infinity,
                            height: 48,
                            child: ElevatedButton(
                              onPressed: authProvider.isLoading ? null : _handleLogin,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.deepPurple,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: authProvider.isLoading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    )
                                  : const Text(
                                      '登录',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // 测试连接按钮
                      SizedBox(
                        width: double.infinity,
                        height: 36,
                        child: OutlinedButton.icon(
                          onPressed: _testConnection,
                          icon: const Icon(Icons.wifi_find, size: 16),
                          label: const Text(
                            '测试后端连接',
                            style: TextStyle(fontSize: 14),
                          ),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.deepPurple,
                            side: const BorderSide(color: Colors.deepPurple),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // 提示信息
                      Text(
                        '首次登录将自动创建账户\n默认密码：123456\n后端地址：http://localhost:8092',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
