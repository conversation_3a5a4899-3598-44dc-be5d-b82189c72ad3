"""
主应用入口点 - DND游戏API服务器
"""
import os
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from api import api_router
from config import HOST, PORT, PLAYERS_DIR, LOGS_DIR, DATA_DIR
from logger import info, debug, error, exception
from models import ApiResponse

# 首先确保日志目录存在
os.makedirs(LOGS_DIR, exist_ok=True)

# 创建FastAPI应用
app = FastAPI(
    title="DND游戏API",
    description="单人龙与地下城RPG游戏后端API服务器",
    version="0.1.0",
    docs_url="/docs",  # Swagger UI文档地址
    redoc_url="/redoc"  # ReDoc文档地址
)

# 添加CORS中间件，允许Flutter前端访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "*",  # 开发环境允许所有来源，生产环境应该限制
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router, prefix="/api")

# 全局异常处理器
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """处理HTTP异常"""
    error(f"HTTP异常: {exc.status_code} - {exc.detail}, 路径: {request.url.path}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ApiResponse(
            success=False,
            message=str(exc.detail),
            data=None
        ).dict()
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证异常"""
    error_details = str(exc)
    error(f"请求验证异常: {error_details}, 路径: {request.url.path}")
    return JSONResponse(
        status_code=422,
        content=ApiResponse(
            success=False,
            message="请求数据验证失败",
            data={"errors": exc.errors()}
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理所有未处理的异常"""
    error(f"未处理异常: {str(exc)}, 路径: {request.url.path}")
    exception(f"路径 {request.url.path} 发生未处理异常")
    return JSONResponse(
        status_code=500,
        content=ApiResponse(
            success=False,
            message="服务器内部错误",
            data=None
        ).dict()
    )

# 注意：前端现在使用Flutter，不再需要静态文件服务

# 初始化目录
def init_directories():
    """初始化必要的目录结构"""
    # 确保players目录存在
    os.makedirs(PLAYERS_DIR, exist_ok=True)
    debug(f"确保玩家数据目录存在: {PLAYERS_DIR}")
    
    # 确保日志目录存在
    os.makedirs(LOGS_DIR, exist_ok=True)
    debug(f"确保日志目录存在: {LOGS_DIR}")
    
    # 确保数据目录存在
    os.makedirs(DATA_DIR, exist_ok=True)
    debug(f"确保游戏数据目录存在: {DATA_DIR}")

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    info("=== DND游戏API服务器正在启动 ===")
    init_directories()

    # 记录所有注册的API路由
    api_routes = []
    for route in app.routes:
        if hasattr(route, "methods") and route.path.startswith("/api"):
            api_routes.append(f"{route.path} - {route.methods}")
            debug(f"已注册API路由: {route.path} - {route.methods}")

    info(f"API服务器启动完成，运行在 http://{HOST}:{PORT}")
    info(f"API文档地址: http://{HOST}:{PORT}/docs")
    info("前端请使用Flutter应用连接到此API服务器")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行"""
    info("=== DND游戏API服务器正在关闭 ===")

# API根路由
@app.get("/api")
async def root():
    """API根路由，返回基本信息"""
    debug("访问API根路由")
    return {
        "name": "DND游戏API服务器",
        "version": "0.1.0",
        "status": "运行中",
        "description": "单人龙与地下城RPG游戏后端API",
        "frontend": "Flutter应用",
        "docs": "/docs"
    }

if __name__ == "__main__":
    import uvicorn
    init_directories()
    info(f"以独立模式启动API服务器，监听地址: {HOST}:{PORT}")
    info("API文档地址: http://localhost:8000/docs")
    uvicorn.run(
        "backend.main:app",
        host=HOST,
        port=PORT,
        reload=True
    )