{"index": "sleep", "name": "Sleep", "desc": ["This spell sends creatures into a magical slumber. Roll 5d8; the total is how many hit points of creatures this spell can affect. Creatures within 20 feet of a point you choose within range are affected in ascending order of their current hit points (ignoring unconscious creatures).", "Starting with the creature that has the lowest current hit points, each creature affected by this spell falls unconscious until the spell ends, the sleeper takes damage, or someone uses an action to shake or slap the sleeper awake. Subtract each creature's hit points from the total before moving on to the creature with the next lowest hit points. A creature's hit points must be equal to or less than the remaining total for that creature to be affected.", "Undead and creatures immune to being charmed aren't affected by this spell."], "higher_level": ["When you cast this spell using a spell slot of 2nd level or higher, roll an additional 2d8 for each slot level above 1st."], "range": "90 feet", "components": ["V", "S", "M"], "material": "A pinch of fine sand, rose petals, or a cricket.", "ritual": false, "duration": "1 minute", "concentration": false, "casting_time": "1 action", "level": 1, "damage": {"damage_at_slot_level": {"1": "5d8"}}, "area_of_effect": {"type": "sphere", "size": 20}, "school": {"index": "enchantment", "name": "Enchantment", "url": "/api/2014/magic-schools/enchantment"}, "classes": [{"index": "bard", "name": "Bard", "url": "/api/2014/classes/bard"}, {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}, {"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [{"index": "lore", "name": "Lore", "url": "/api/2014/subclasses/lore"}], "url": "/api/2014/spells/sleep", "updated_at": "2025-04-08T21:14:16.147Z"}