{"higher_level": [], "index": "polymorph", "name": "Polymorph", "desc": ["This spell transforms a creature that you can see within range into a new form. An unwilling creature must make a wisdom saving throw to avoid the effect. A shapechanger automatically succeeds on this saving throw.", "The transformation lasts for the duration, or until the target drops to 0 hit points or dies. The new form can be any beast whose challenge rating is equal to or less than the target's (or the target's level, if it doesn't have a challenge rating). The target's game statistics, including mental ability scores, are replaced by the statistics of the chosen beast. It retains its alignment and personality.", "The target assumes the hit points of its new form. When it reverts to its normal form, the creature returns to the number of hit points it had before it transformed. If it reverts as a result of dropping to 0 hit points, any excess damage carries over to its normal form. As long as the excess damage doesn't reduce the creature's normal form to 0 hit points, it isn't knocked unconscious.", "The creature is limited in the actions it can perform by the nature of its new form, and it can't speak, cast spells, or take any other action that requires hands or speech.", "The target's gear melds into the new form. The creature can't activate, use, wield, or otherwise benefit from any of its equipment."], "range": "60 feet", "components": ["V", "S", "M"], "material": "A caterpillar cocoon.", "ritual": false, "duration": "Up to 1 hour", "concentration": true, "casting_time": "1 action", "level": 4, "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_success": "none"}, "school": {"index": "transmutation", "name": "Transmutation", "url": "/api/2014/magic-schools/transmutation"}, "classes": [{"index": "bard", "name": "Bard", "url": "/api/2014/classes/bard"}, {"index": "druid", "name": "Druid", "url": "/api/2014/classes/druid"}, {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}, {"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [], "url": "/api/2014/spells/polymorph", "updated_at": "2025-04-08T21:14:16.147Z"}