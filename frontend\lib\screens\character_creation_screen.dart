import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/character_provider.dart';
import '../models/character.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';
import '../widgets/custom_button.dart';
import '../widgets/loading_dialog.dart';

/// 角色创建界面
class CharacterCreationScreen extends StatefulWidget {
  const CharacterCreationScreen({Key? key}) : super(key: key);

  @override
  State<CharacterCreationScreen> createState() => _CharacterCreationScreenState();
}

class _CharacterCreationScreenState extends State<CharacterCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ageController = TextEditingController(text: '20');

  String _selectedRace = AppConstants.races.first;
  String _selectedClass = AppConstants.classes.first;
  String _selectedGender = AppConstants.genders.first;

  CharacterAttributes _attributes = CharacterAttributes(
    strength: 10,
    dexterity: 10,
    mind: 10,
  );

  @override
  void initState() {
    super.initState();
    _generateRandomAttributes();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    super.dispose();
  }

  /// 随机生成属性
  void _generateRandomAttributes() {
    final characterProvider = Provider.of<CharacterProvider>(context, listen: false);
    setState(() {
      _attributes = characterProvider.generateRandomAttributes();
    });
  }

  /// 保存角色
  void _saveCharacter() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final characterProvider = Provider.of<CharacterProvider>(context, listen: false);

    if (authProvider.user == null) {
      ErrorDialog.show(context, message: '用户信息丢失，请重新登录');
      return;
    }

    // 应用种族加成
    final finalAttributes = characterProvider.applyRacialBonus(_attributes, _selectedRace);

    // 计算派生属性
    final derivedStats = characterProvider.calculateDerivedStats(finalAttributes, _selectedClass);

    // 计算技能
    final skills = characterProvider.calculateSkills(_selectedClass, _selectedRace);

    // 创建角色请求
    final request = CreateCharacterRequest(
      username: authProvider.user!.username,
      characterName: _nameController.text.trim(),
      race: _selectedRace,
      characterClass: _selectedClass,
      gender: _selectedGender,
      age: int.parse(_ageController.text),
      attributes: finalAttributes,
      derivedStats: derivedStats,
      skills: skills,
      currency: 100, // 初始金币
    );

    final success = await characterProvider.createCharacter(request);

    if (success) {
      // 更新认证提供者中的角色列表
      authProvider.updateUserCharacters(characterProvider.characters);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('角色创建成功')),
        );
        Navigator.of(context).pop();
      }
    } else if (characterProvider.error != null) {
      if (mounted) {
        ErrorDialog.show(
          context,
          message: characterProvider.error!,
          onConfirm: () => characterProvider.clearError(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('创建角色'),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 基本信息
                    _buildSectionTitle('基本信息'),
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),

                    // 属性值
                    _buildSectionTitle('属性值'),
                    _buildAttributesSection(),
                    const SizedBox(height: 24),

                    // 派生属性预览
                    _buildSectionTitle('派生属性'),
                    _buildDerivedStatsSection(),
                  ],
                ),
              ),
            ),

            // 操作按钮
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: SecondaryButton(
                      text: '随机属性',
                      onPressed: _generateRandomAttributes,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Consumer<CharacterProvider>(
                      builder: (context, characterProvider, child) {
                        return CustomButton(
                          text: '保存角色',
                          onPressed: _saveCharacter,
                          isLoading: characterProvider.isLoading,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 构建基本信息部分
  Widget _buildBasicInfoSection() {
    return Column(
      children: [
        // 角色名
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: '角色名',
            border: OutlineInputBorder(),
          ),
          validator: Validators.validateCharacterName,
        ),
        const SizedBox(height: 16),

        // 种族和职业
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedRace,
                decoration: const InputDecoration(
                  labelText: '种族',
                  border: OutlineInputBorder(),
                ),
                items: AppConstants.races.map((race) {
                  return DropdownMenuItem(value: race, child: Text(race));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedRace = value!;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedClass,
                decoration: const InputDecoration(
                  labelText: '职业',
                  border: OutlineInputBorder(),
                ),
                items: AppConstants.classes.map((cls) {
                  return DropdownMenuItem(value: cls, child: Text(cls));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedClass = value!;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // 性别和年龄
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedGender,
                decoration: const InputDecoration(
                  labelText: '性别',
                  border: OutlineInputBorder(),
                ),
                items: AppConstants.genders.map((gender) {
                  return DropdownMenuItem(value: gender, child: Text(gender));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedGender = value!;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _ageController,
                decoration: const InputDecoration(
                  labelText: '年龄',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: Validators.validateAge,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建属性值部分
  Widget _buildAttributesSection() {
    return Column(
      children: [
        _buildAttributeRow('力量 (STR)', _attributes.strength),
        const SizedBox(height: 12),
        _buildAttributeRow('敏捷 (DEX)', _attributes.dexterity),
        const SizedBox(height: 12),
        _buildAttributeRow('智力 (MIND)', _attributes.mind),
        const SizedBox(height: 12),

        // 种族加成提示
        if (_selectedRace != '人类')
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.info, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getRacialBonusText(_selectedRace),
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// 构建属性行
  Widget _buildAttributeRow(String label, int value) {
    final modifier = (value - 10) ~/ 2;
    final modifierText = modifier >= 0 ? '+$modifier' : '$modifier';

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            '$value ($modifierText)',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// 构建派生属性部分
  Widget _buildDerivedStatsSection() {
    final characterProvider = Provider.of<CharacterProvider>(context, listen: false);
    final finalAttributes = characterProvider.applyRacialBonus(_attributes, _selectedRace);
    final derivedStats = characterProvider.calculateDerivedStats(finalAttributes, _selectedClass);
    final skills = characterProvider.calculateSkills(_selectedClass, _selectedRace);

    return Column(
      children: [
        // HP和AC
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                '生命值',
                '${derivedStats.hp}',
                '基础: ${derivedStats.hpBreakdown.base} + 奖励: ${derivedStats.hpBreakdown.bonus}',
                Icons.favorite,
                Colors.red,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                '护甲等级',
                '${derivedStats.ac}',
                '基础: ${derivedStats.acBreakdown.base} + 敏捷: ${derivedStats.acBreakdown.dex}',
                Icons.shield,
                Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // 技能
        _buildSkillsPreview(skills),
      ],
    );
  }

  /// 构建属性卡片
  Widget _buildStatCard(String title, String value, String breakdown, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            breakdown,
            style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建技能预览
  Widget _buildSkillsPreview(Skills skills) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '技能值',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildSkillItem('体能', skills.physical.total)),
              Expanded(child: _buildSkillItem('潜行', skills.subterfuge.total)),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(child: _buildSkillItem('知识', skills.knowledge.total)),
              Expanded(child: _buildSkillItem('交流', skills.communication.total)),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建技能项
  Widget _buildSkillItem(String name, int value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(name, style: const TextStyle(fontSize: 14)),
          Text(
            '$value',
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// 获取种族加成文本
  String _getRacialBonusText(String race) {
    switch (race) {
      case '矮人':
        return '种族加成：力量 +2';
      case '精灵':
        return '种族加成：智力 +2';
      case '半身人':
        return '种族加成：敏捷 +2';
      default:
        return '';
    }
  }
}
