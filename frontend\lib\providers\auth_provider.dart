import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/character.dart';
import '../services/auth_service.dart';
import '../utils/debug_logger.dart';

/// 认证状态管理
class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  
  User? _user;
  bool _isLoading = false;
  String? _error;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null;

  /// 用户登录
  Future<bool> login(String username, String password) async {
    DebugLogger.userAction('开始登录', params: {'username': username});
    _setLoading(true);
    _clearError();

    try {
      DebugLogger.debug('调用认证服务登录', tag: 'AUTH');
      final user = await _authService.login(username, password);
      _user = user;
      DebugLogger.info('登录成功: ${user.username}', tag: 'AUTH');
      DebugLogger.debug('用户角色数量: ${user.characters.length}', tag: 'AUTH');
      notifyListeners();
      return true;
    } catch (e) {
      DebugLogger.error('登录失败', tag: 'AUTH', error: e);
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 用户登出
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await _authService.logout();
      _user = null;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 检查登录状态
  Future<void> checkLoginStatus() async {
    _setLoading(true);
    
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        final username = await _authService.getSavedUsername();
        if (username != null) {
          // 这里可以尝试自动登录或者只是设置用户名
          // 为了简化，我们只清除登录状态，让用户重新登录
          await _authService.logout();
        }
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 更新用户角色列表
  void updateUserCharacters(List<CharacterSummary> characters) {
    if (_user != null) {
      _user = _user!.copyWith(characters: characters);
      notifyListeners();
    }
  }

  /// 保存最后选择的角色ID
  Future<void> saveLastCharacterId(String characterId) async {
    await _authService.saveLastCharacterId(characterId);
  }

  /// 获取最后选择的角色ID
  Future<String?> getLastCharacterId() async {
    return await _authService.getLastCharacterId();
  }

  /// 获取保存的用户名
  Future<String?> getSavedUsername() async {
    return await _authService.getSavedUsername();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    DebugLogger.stateChange('AuthProvider', 'loading: $_isLoading', 'loading: $loading');
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    DebugLogger.error('认证错误: $error', tag: 'AUTH');
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    if (_error != null) {
      DebugLogger.debug('清除认证错误', tag: 'AUTH');
    }
    _error = null;
    notifyListeners();
  }

  /// 清除错误（供UI调用）
  void clearError() {
    _clearError();
  }
}
