{"index": "adult-white-dragon", "name": "Adult White Dragon", "size": "<PERSON>ge", "type": "dragon", "alignment": "chaotic evil", "armor_class": [{"type": "natural", "value": 18}], "hit_points": 200, "hit_dice": "16d12", "hit_points_roll": "16d12+96", "speed": {"walk": "40 ft.", "burrow": "30 ft.", "fly": "80 ft.", "swim": "40 ft."}, "strength": 22, "dexterity": 10, "constitution": 22, "intelligence": 8, "wisdom": 12, "charisma": 12, "proficiencies": [{"value": 5, "proficiency": {"index": "saving-throw-dex", "name": "Saving Throw: DEX", "url": "/api/2014/proficiencies/saving-throw-dex"}}, {"value": 11, "proficiency": {"index": "saving-throw-con", "name": "Saving Throw: CON", "url": "/api/2014/proficiencies/saving-throw-con"}}, {"value": 6, "proficiency": {"index": "saving-throw-wis", "name": "Saving Throw: WIS", "url": "/api/2014/proficiencies/saving-throw-wis"}}, {"value": 6, "proficiency": {"index": "saving-throw-cha", "name": "Saving Throw: CHA", "url": "/api/2014/proficiencies/saving-throw-cha"}}, {"value": 11, "proficiency": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"value": 5, "proficiency": {"index": "skill-stealth", "name": "Skill: <PERSON>eal<PERSON>", "url": "/api/2014/proficiencies/skill-stealth"}}], "damage_vulnerabilities": [], "damage_resistances": [], "damage_immunities": ["cold"], "condition_immunities": [], "senses": {"blindsight": "60 ft.", "darkvision": "120 ft.", "passive_perception": 21}, "languages": "Common, Draconic", "challenge_rating": 13, "proficiency_bonus": 5, "xp": 10000, "special_abilities": [{"name": "Ice Walk", "desc": "The dragon can move across and climb icy surfaces without needing to make an ability check. Additionally, difficult terrain composed of ice or snow doesn't cost it extra moment.", "damage": []}, {"name": "Legendary Resistance", "desc": "If the dragon fails a saving throw, it can choose to succeed instead.", "usage": {"type": "per day", "times": 3, "rest_types": []}, "damage": []}], "actions": [{"name": "Multiattack", "multiattack_type": "actions", "desc": "The dragon can use its Frightful Presence. It then makes three attacks: one with its bite and two with its claws.", "actions": [{"action_name": "Frightful Presence", "count": "1", "type": "ability"}, {"action_name": "Bite", "count": "1", "type": "melee"}, {"action_name": "Claw", "count": "2", "type": "melee"}], "damage": []}, {"name": "Bite", "desc": "Melee Weapon Attack: +11 to hit, reach 10 ft., one target. Hit: 17 (2d10 + 6) piercing damage plus 4 (1d8) cold damage.", "attack_bonus": 11, "damage": [{"damage_type": {"index": "piercing", "name": "Piercing", "url": "/api/2014/damage-types/piercing"}, "damage_dice": "2d10+6"}, {"damage_type": {"index": "cold", "name": "Cold", "url": "/api/2014/damage-types/cold"}, "damage_dice": "1d8"}], "actions": []}, {"name": "Claw", "desc": "Melee Weapon Attack: +11 to hit, reach 5 ft., one target. Hit: 13 (2d6 + 6) slashing damage.", "attack_bonus": 11, "damage": [{"damage_type": {"index": "slashing", "name": "Slashing", "url": "/api/2014/damage-types/slashing"}, "damage_dice": "2d6+6"}], "actions": []}, {"name": "Tail", "desc": "Melee Weapon Attack: +11 to hit, reach 15 ft., one target. Hit: 15 (2d8 + 6) bludgeoning damage.", "attack_bonus": 11, "damage": [{"damage_type": {"index": "bludgeoning", "name": "Bludgeoning", "url": "/api/2014/damage-types/bludgeoning"}, "damage_dice": "2d8+6"}], "actions": []}, {"name": "Frightful Presence", "desc": "Each creature of the dragon's choice that is within 120 ft. of the dragon and aware of it must succeed on a DC 14 Wisdom saving throw or become frightened for 1 minute. A creature can repeat the saving throw at the end of each of its turns, ending the effect on itself on a success. If a creature's saving throw is successful or the effect ends for it, the creature is immune to the dragon's Frightful Presence for the next 24 hours.", "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_value": 14, "success_type": "none"}, "damage": [], "actions": []}, {"name": "Cold Breath", "desc": "The dragon exhales an icy blast in a 60-foot cone. Each creature in that area must make a DC 19 Constitution saving throw, taking 54 (12d8) cold damage on a failed save, or half as much damage on a successful one.", "usage": {"type": "recharge on roll", "dice": "1d6", "min_value": 5}, "dc": {"dc_type": {"index": "con", "name": "CON", "url": "/api/2014/ability-scores/con"}, "dc_value": 19, "success_type": "half"}, "damage": [{"damage_type": {"index": "cold", "name": "Cold", "url": "/api/2014/damage-types/cold"}, "damage_dice": "12d8"}], "actions": []}], "legendary_actions": [{"name": "Detect", "desc": "The dragon makes a Wisdom (Perception) check.", "damage": []}, {"name": "Tail Attack", "desc": "The dragon makes a tail attack.", "damage": []}, {"name": "Wing Attack (Costs 2 Actions)", "desc": "The dragon beats its wings. Each creature within 10 ft. of the dragon must succeed on a DC 19 Dexterity saving throw or take 13 (2d6 + 6) bludgeoning damage and be knocked prone. The dragon can then fly up to half its flying speed.", "dc": {"dc_type": {"index": "dex", "name": "DEX", "url": "/api/2014/ability-scores/dex"}, "dc_value": 19, "success_type": "none"}, "damage": [{"damage_type": {"index": "bludgeoning", "name": "Bludgeoning", "url": "/api/2014/damage-types/bludgeoning"}, "damage_dice": "2d6+6"}]}], "image": "/api/images/monsters/adult-white-dragon.png", "url": "/api/2014/monsters/adult-white-dragon", "updated_at": "2025-05-04T02:15:02.221Z", "forms": [], "reactions": []}