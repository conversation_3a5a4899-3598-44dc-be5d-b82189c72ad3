import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../utils/api_config.dart';
import '../utils/debug_logger.dart';
import '../models/api_response.dart';

/// 基础API服务类
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  /// 发送GET请求
  Future<Map<String, dynamic>> get(String endpoint) async {
    DebugLogger.apiRequest('GET', endpoint);
    try {
      final uri = Uri.parse(endpoint);
      DebugLogger.debug('发送GET请求到: $uri', tag: 'API');

      final response = await http.get(
        uri,
        headers: ApiConfig.defaultHeaders,
      ).timeout(Duration(seconds: ApiConfig.timeoutSeconds));

      DebugLogger.apiResponse(endpoint, response.statusCode, data: response.body);
      return _handleResponse(response);
    } catch (e) {
      DebugLogger.error('GET请求失败: $endpoint', tag: 'API', error: e);
      throw _handleError(e);
    }
  }

  /// 发送POST请求
  Future<Map<String, dynamic>> post(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    DebugLogger.apiRequest('POST', endpoint, data: data);
    try {
      final uri = Uri.parse(endpoint);
      final jsonBody = jsonEncode(data);
      DebugLogger.debug('发送POST请求到: $uri', tag: 'API');
      DebugLogger.debug('请求体: $jsonBody', tag: 'API');

      final response = await http.post(
        uri,
        headers: ApiConfig.defaultHeaders,
        body: jsonBody,
      ).timeout(Duration(seconds: ApiConfig.timeoutSeconds));

      DebugLogger.apiResponse(endpoint, response.statusCode, data: response.body);
      return _handleResponse(response);
    } catch (e) {
      DebugLogger.error('POST请求失败: $endpoint', tag: 'API', error: e);
      throw _handleError(e);
    }
  }

  /// 发送PUT请求
  Future<Map<String, dynamic>> put(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    try {
      final uri = Uri.parse(endpoint);
      final response = await http.put(
        uri,
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode(data),
      ).timeout(Duration(seconds: ApiConfig.timeoutSeconds));

      return _handleResponse(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// 发送DELETE请求
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, dynamic>? data,
  }) async {
    try {
      final uri = Uri.parse(endpoint);
      final response = await http.delete(
        uri,
        headers: ApiConfig.defaultHeaders,
        body: data != null ? jsonEncode(data) : null,
      ).timeout(Duration(seconds: ApiConfig.timeoutSeconds));

      return _handleResponse(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// 处理HTTP响应
  Map<String, dynamic> _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final body = response.body;

    DebugLogger.debug('处理HTTP响应: 状态码=$statusCode', tag: 'API');
    DebugLogger.debug('响应体: $body', tag: 'API');

    // 尝试解析JSON
    Map<String, dynamic> jsonData;
    try {
      jsonData = jsonDecode(body);
      DebugLogger.debug('JSON解析成功', tag: 'API');
    } catch (e) {
      DebugLogger.error('JSON解析失败', tag: 'API', error: e);
      throw ApiException('响应格式错误: $body');
    }

    // 检查HTTP状态码
    if (statusCode >= 200 && statusCode < 300) {
      DebugLogger.debug('HTTP请求成功: $statusCode', tag: 'API');
      return jsonData;
    } else if (statusCode == 401) {
      DebugLogger.error('认证失败: $statusCode', tag: 'API');
      throw ApiException(jsonData['detail'] ?? '认证失败');
    } else if (statusCode == 400) {
      DebugLogger.error('请求参数错误: $statusCode', tag: 'API');
      throw ApiException(jsonData['detail'] ?? '请求参数错误');
    } else if (statusCode == 404) {
      DebugLogger.error('资源不存在: $statusCode', tag: 'API');
      throw ApiException('请求的资源不存在');
    } else if (statusCode >= 500) {
      DebugLogger.error('服务器错误: $statusCode', tag: 'API');
      throw ApiException('服务器内部错误');
    } else {
      DebugLogger.error('请求失败: $statusCode', tag: 'API');
      throw ApiException(jsonData['detail'] ?? '请求失败');
    }
  }

  /// 处理异常
  ApiException _handleError(dynamic error) {
    DebugLogger.error('API异常处理', tag: 'API', error: error);

    if (error is ApiException) {
      return error;
    } else if (error is SocketException) {
      DebugLogger.network('网络连接失败: ${error.message}', isError: true);
      return ApiException('网络连接失败，请检查网络设置和后端服务器状态');
    } else if (error is HttpException) {
      DebugLogger.network('HTTP异常: ${error.message}', isError: true);
      return ApiException('HTTP请求失败: ${error.message}');
    } else if (error.toString().contains('TimeoutException')) {
      DebugLogger.network('请求超时', isError: true);
      return ApiException('请求超时，请稍后重试');
    } else {
      DebugLogger.error('未知API错误: ${error.toString()}', tag: 'API');
      return ApiException('未知错误: ${error.toString()}');
    }
  }
}

/// API异常类
class ApiException implements Exception {
  final String message;

  ApiException(this.message);

  @override
  String toString() => 'ApiException: $message';
}
