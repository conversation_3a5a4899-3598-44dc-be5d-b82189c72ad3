"""
认证服务，负责处理用户登录、验证等功能
"""
import os
import json
from pathlib import Path
from typing import Tuple, List, Dict, Optional
import datetime

from models import Account, Character
from config import PLAYERS_DIR, DEFAULT_PASSWORD

class AuthService:
    @staticmethod
    def login(username: str, password: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Tuple[bool, str, Dict]: (成功标志, 消息, 用户数据)
        """
        user_dir = PLAYERS_DIR / username
        
        # 检查用户目录是否存在
        if not user_dir.exists():
            # 创建新用户
            return AuthService.create_account(username, password)
        
        # 读取account.json文件
        account_file = user_dir / "account.json"
        if account_file.exists():
            try:
                with open(account_file, "r", encoding="utf-8") as f:
                    account_data = json.load(f)
                
                # 验证密码
                if account_data.get("password") == password:
                    # 获取角色列表
                    characters = AuthService.get_character_list(username)
                    return True, "登录成功", {"username": username, "characters": characters}
                else:
                    return False, "密码错误", None
            except Exception as e:
                return False, f"读取账户数据出错: {str(e)}", None
        else:
            # 创建新账户文件
            return AuthService.create_account(username, password)
    
    @staticmethod
    def create_account(username: str, password: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        创建新账户
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Tuple[bool, str, Dict]: (成功标志, 消息, 用户数据)
        """
        try:
            # 创建用户目录
            user_dir = PLAYERS_DIR / username
            user_dir.mkdir(exist_ok=True)
            
            # 创建characters目录
            chars_dir = user_dir / "characters"
            chars_dir.mkdir(exist_ok=True)
            
            # 创建account.json文件
            account = Account(username=username, password=password or DEFAULT_PASSWORD)
            account_file = user_dir / "account.json"
            
            with open(account_file, "w", encoding="utf-8") as f:
                f.write(account.json(ensure_ascii=False, indent=4))
            
            return True, "账户创建成功", {"username": username, "characters": []}
        except Exception as e:
            return False, f"创建账户失败: {str(e)}", None
    
    @staticmethod
    def get_character_list(username: str) -> List[Dict]:
        """
        获取用户的角色列表
        
        Args:
            username: 用户名
            
        Returns:
            List[Dict]: 角色列表
        """
        characters = []
        chars_dir = PLAYERS_DIR / username / "characters"
        
        if chars_dir.exists():
            for char_file in chars_dir.glob("*.json"):
                try:
                    with open(char_file, "r", encoding="utf-8") as f:
                        char_data = json.load(f)
                        characters.append(char_data)
                except Exception:
                    # 跳过错误的文件
                    continue
        
        return characters 