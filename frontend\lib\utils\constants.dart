/// 应用常量定义
class AppConstants {
  // API相关常量
  static const String baseUrl = 'http://localhost:8092/api';
  
  // 路由名称
  static const String loginRoute = '/login';
  static const String characterSelectionRoute = '/character-selection';
  static const String characterCreationRoute = '/character-creation';
  static const String gameRoute = '/game';
  
  // 本地存储键名
  static const String userTokenKey = 'user_token';
  static const String usernameKey = 'username';
  static const String lastCharacterIdKey = 'last_character_id';
  
  // 游戏配置
  static const int maxCharactersPerAccount = 5;
  static const String defaultPassword = '123456';
  
  // 种族列表
  static const List<String> races = [
    '人类',
    '精灵',
    '矮人',
    '半身人',
  ];
  
  // 职业列表
  static const List<String> classes = [
    '战士',
    '法师',
    '盗贼',
    '牧师',
  ];
  
  // 性别列表
  static const List<String> genders = [
    '男',
    '女',
  ];
  
  // 属性名称映射
  static const Map<String, String> attributeNames = {
    'STR': '力量',
    'DEX': '敏捷',
    'MIND': '智力',
  };
  
  // 技能名称映射
  static const Map<String, String> skillNames = {
    'Physical': '体能',
    'Subterfuge': '潜行',
    'Knowledge': '知识',
    'Communication': '交流',
  };
}
