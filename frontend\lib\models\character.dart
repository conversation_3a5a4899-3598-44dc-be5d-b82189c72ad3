import 'package:json_annotation/json_annotation.dart';

part 'character.g.dart';

/// 角色基础信息
@JsonSerializable()
class BasicInfo {
  final String name;
  final String player;
  final String race;
  @JsonKey(name: 'class')
  final String characterClass;
  final int level;
  final int exp;
  final int age;
  final String gender;

  const BasicInfo({
    required this.name,
    required this.player,
    required this.race,
    required this.characterClass,
    required this.level,
    required this.exp,
    required this.age,
    required this.gender,
  });

  factory BasicInfo.fromJson(Map<String, dynamic> json) => _$BasicInfoFromJson(json);
  Map<String, dynamic> toJson() => _$BasicInfoToJson(this);
}

/// 角色摘要信息（用于列表显示）
@JsonSerializable()
class CharacterSummary {
  final String id;
  final BasicInfo basic;

  const CharacterSummary({
    required this.id,
    required this.basic,
  });

  // 便捷访问器
  String get name => basic.name;
  String get race => basic.race;
  String get characterClass => basic.characterClass;
  int get level => basic.level;

  factory CharacterSummary.fromJson(Map<String, dynamic> json) =>
      _$CharacterSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$CharacterSummaryToJson(this);
}

/// 角色属性
@JsonSerializable()
class CharacterAttributes {
  @JsonKey(name: 'STR')
  final int strength;
  @JsonKey(name: 'DEX')
  final int dexterity;
  @JsonKey(name: 'MIND')
  final int mind;

  CharacterAttributes({
    required this.strength,
    required this.dexterity,
    required this.mind,
  });

  factory CharacterAttributes.fromJson(Map<String, dynamic> json) =>
      _$CharacterAttributesFromJson(json);

  Map<String, dynamic> toJson() => _$CharacterAttributesToJson(this);

  /// 计算属性修正值
  int getModifier(int attributeValue) {
    return (attributeValue - 10) ~/ 2;
  }

  /// 获取力量修正值
  int get strengthModifier => getModifier(strength);

  /// 获取敏捷修正值
  int get dexterityModifier => getModifier(dexterity);

  /// 获取智力修正值
  int get mindModifier => getModifier(mind);
}

/// 派生属性
@JsonSerializable()
class DerivedStats {
  final int hp;
  @JsonKey(name: 'hp_breakdown')
  final HpBreakdown hpBreakdown;
  final int ac;
  @JsonKey(name: 'ac_breakdown')
  final AcBreakdown acBreakdown;

  DerivedStats({
    required this.hp,
    required this.hpBreakdown,
    required this.ac,
    required this.acBreakdown,
  });

  factory DerivedStats.fromJson(Map<String, dynamic> json) =>
      _$DerivedStatsFromJson(json);

  Map<String, dynamic> toJson() => _$DerivedStatsToJson(this);
}

/// HP详细分解
@JsonSerializable()
class HpBreakdown {
  final int base;
  final int bonus;

  HpBreakdown({
    required this.base,
    required this.bonus,
  });

  factory HpBreakdown.fromJson(Map<String, dynamic> json) =>
      _$HpBreakdownFromJson(json);

  Map<String, dynamic> toJson() => _$HpBreakdownToJson(this);
}

/// AC详细分解
@JsonSerializable()
class AcBreakdown {
  final int base;
  final int dex;
  final int armor;
  final int shield;
  final int misc;

  AcBreakdown({
    required this.base,
    required this.dex,
    required this.armor,
    required this.shield,
    required this.misc,
  });

  factory AcBreakdown.fromJson(Map<String, dynamic> json) =>
      _$AcBreakdownFromJson(json);

  Map<String, dynamic> toJson() => _$AcBreakdownToJson(this);
}

/// 技能信息
@JsonSerializable()
class Skills {
  @JsonKey(name: 'Physical')
  final SkillDetail physical;
  @JsonKey(name: 'Subterfuge')
  final SkillDetail subterfuge;
  @JsonKey(name: 'Knowledge')
  final SkillDetail knowledge;
  @JsonKey(name: 'Communication')
  final SkillDetail communication;

  Skills({
    required this.physical,
    required this.subterfuge,
    required this.knowledge,
    required this.communication,
  });

  factory Skills.fromJson(Map<String, dynamic> json) => _$SkillsFromJson(json);

  Map<String, dynamic> toJson() => _$SkillsToJson(this);
}

/// 技能详情
@JsonSerializable()
class SkillDetail {
  final int base;
  @JsonKey(name: 'class_bonus')
  final int classBonus;
  @JsonKey(name: 'race_bonus')
  final int raceBonus;

  SkillDetail({
    required this.base,
    required this.classBonus,
    required this.raceBonus,
  });

  factory SkillDetail.fromJson(Map<String, dynamic> json) =>
      _$SkillDetailFromJson(json);

  Map<String, dynamic> toJson() => _$SkillDetailToJson(this);

  /// 计算总技能值
  int get total => base + classBonus + raceBonus;
}

/// 角色创建请求模型
@JsonSerializable()
class CreateCharacterRequest {
  final String username;
  @JsonKey(name: 'character_name')
  final String characterName;
  final String race;
  @JsonKey(name: 'character_class')
  final String characterClass;
  final String gender;
  final int age;
  final CharacterAttributes attributes;
  @JsonKey(name: 'derived_stats')
  final DerivedStats derivedStats;
  final Skills skills;
  final int currency;

  CreateCharacterRequest({
    required this.username,
    required this.characterName,
    required this.race,
    required this.characterClass,
    required this.gender,
    required this.age,
    required this.attributes,
    required this.derivedStats,
    required this.skills,
    required this.currency,
  });

  factory CreateCharacterRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateCharacterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateCharacterRequestToJson(this);
}

/// 删除角色请求模型
@JsonSerializable()
class DeleteCharacterRequest {
  final String username;
  @JsonKey(name: 'character_id')
  final String characterId;

  DeleteCharacterRequest({
    required this.username,
    required this.characterId,
  });

  factory DeleteCharacterRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteCharacterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteCharacterRequestToJson(this);
}
