import 'package:flutter/material.dart';
import '../models/character.dart';

/// 角色卡片组件
class CharacterCard extends StatelessWidget {
  final CharacterSummary character;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const CharacterCard({
    Key? key,
    required this.character,
    this.isSelected = false,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 8 : 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: Theme.of(context).primaryColor, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 角色头像占位符
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _getRaceColor(character.race),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  _getClassIcon(character.characterClass),
                  color: Colors.white,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              
              // 角色信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      character.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${character.race} ${character.characterClass}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '等级 ${character.level}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              // 删除按钮
              if (onDelete != null)
                IconButton(
                  onPressed: () => _showDeleteConfirmation(context),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red,
                  tooltip: '删除角色',
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 根据种族获取颜色
  Color _getRaceColor(String race) {
    switch (race) {
      case '人类':
        return Colors.blue;
      case '精灵':
        return Colors.green;
      case '矮人':
        return Colors.brown;
      case '半身人':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// 根据职业获取图标
  IconData _getClassIcon(String characterClass) {
    switch (characterClass) {
      case '战士':
        return Icons.shield;
      case '法师':
        return Icons.auto_fix_high;
      case '盗贼':
        return Icons.visibility_off;
      case '牧师':
        return Icons.healing;
      default:
        return Icons.person;
    }
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除角色 "${character.name}" 吗？此操作无法撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onDelete?.call();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }
}
