{"index": "mass-suggestion", "name": "Mass Suggestion", "desc": ["You suggest a course of activity (limited to a sentence or two) and magically influence up to twelve creatures of your choice that you can see within range and that can hear and understand you. Creatures that can't be charmed are immune to this effect. The suggestion must be worded in such a manner as to make the course of action sound reasonable. Asking the creature to stab itself, throw itself onto a spear, immolate itself, or do some other obviously harmful act automatically negates the effect of the spell.", "Each target must make a wisdom saving throw. On a failed save, it pursues the course of action you described to the best of its ability. The suggested course of action can continue for the entire duration. If the suggested activity can be completed in a shorter time, the spell ends when the subject finishes what it was asked to do.", "You can also specify conditions that will trigger a special activity during the duration. For example, you might suggest that a group of soldiers give all their money to the first beggar they meet. If the condition isn't met before the spell ends, the activity isn't performed.", "If you or any of your companions damage a creature affected by this spell, the spell ends for that creature."], "higher_level": ["When you cast this spell using a 7th-level spell slot, the duration is 10 days. When you use an 8th-level spell slot, the duration is 30 days. When you use a 9th-level spell slot, the duration is a year and a day."], "range": "60 feet", "components": ["V", "M"], "material": "A snake's tongue and either a bit of honeycomb or a drop of sweet oil.", "ritual": false, "duration": "24 hours", "concentration": false, "casting_time": "1 action", "level": 6, "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_success": "none"}, "school": {"index": "enchantment", "name": "Enchantment", "url": "/api/2014/magic-schools/enchantment"}, "classes": [{"index": "bard", "name": "Bard", "url": "/api/2014/classes/bard"}, {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}, {"index": "warlock", "name": "<PERSON><PERSON>", "url": "/api/2014/classes/warlock"}, {"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [], "url": "/api/2014/spells/mass-suggestion", "updated_at": "2025-04-08T21:14:16.147Z"}