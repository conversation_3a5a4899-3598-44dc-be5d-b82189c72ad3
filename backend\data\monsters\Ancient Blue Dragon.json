{"index": "ancient-blue-dragon", "name": "Ancient Blue Dragon", "size": "Gargantuan", "type": "dragon", "alignment": "lawful evil", "armor_class": [{"type": "natural", "value": 22}], "hit_points": 481, "hit_dice": "26d20", "hit_points_roll": "26d20+208", "speed": {"walk": "40 ft.", "burrow": "40 ft.", "fly": "80 ft."}, "strength": 29, "dexterity": 10, "constitution": 27, "intelligence": 18, "wisdom": 17, "charisma": 21, "proficiencies": [{"value": 7, "proficiency": {"index": "saving-throw-dex", "name": "Saving Throw: DEX", "url": "/api/2014/proficiencies/saving-throw-dex"}}, {"value": 15, "proficiency": {"index": "saving-throw-con", "name": "Saving Throw: CON", "url": "/api/2014/proficiencies/saving-throw-con"}}, {"value": 10, "proficiency": {"index": "saving-throw-wis", "name": "Saving Throw: WIS", "url": "/api/2014/proficiencies/saving-throw-wis"}}, {"value": 12, "proficiency": {"index": "saving-throw-cha", "name": "Saving Throw: CHA", "url": "/api/2014/proficiencies/saving-throw-cha"}}, {"value": 17, "proficiency": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"value": 7, "proficiency": {"index": "skill-stealth", "name": "Skill: <PERSON>eal<PERSON>", "url": "/api/2014/proficiencies/skill-stealth"}}], "damage_vulnerabilities": [], "damage_resistances": [], "damage_immunities": ["lightning"], "condition_immunities": [], "senses": {"blindsight": "60 ft.", "darkvision": "120 ft.", "passive_perception": 27}, "languages": "Common, Draconic", "challenge_rating": 23, "proficiency_bonus": 7, "xp": 50000, "special_abilities": [{"name": "Legendary Resistance", "desc": "If the dragon fails a saving throw, it can choose to succeed instead.", "usage": {"type": "per day", "times": 3, "rest_types": []}, "damage": []}], "actions": [{"name": "Multiattack", "multiattack_type": "actions", "desc": "The dragon can use its Frightful Presence. It then makes three attacks: one with its bite and two with its claws.", "actions": [{"action_name": "Frightful Presence", "count": "1", "type": "ability"}, {"action_name": "Bite", "count": "1", "type": "melee"}, {"action_name": "Claw", "count": "2", "type": "melee"}], "damage": []}, {"name": "Bite", "desc": "Melee Weapon Attack: +16 to hit, reach 15 ft., one target. Hit: 20 (2d10 + 9) piercing damage plus 11 (2d10) lightning damage.", "attack_bonus": 16, "damage": [{"damage_type": {"index": "piercing", "name": "Piercing", "url": "/api/2014/damage-types/piercing"}, "damage_dice": "2d10+9"}, {"damage_type": {"index": "lightning", "name": "Lightning", "url": "/api/2014/damage-types/lightning"}, "damage_dice": "2d10"}], "actions": []}, {"name": "Claw", "desc": "Melee Weapon Attack: +16 to hit, reach 10 ft., one target. Hit: 16 (2d6 + 9) slashing damage.", "attack_bonus": 16, "damage": [{"damage_type": {"index": "slashing", "name": "Slashing", "url": "/api/2014/damage-types/slashing"}, "damage_dice": "2d6+9"}], "actions": []}, {"name": "Tail", "desc": "Melee Weapon Attack: +16 to hit, reach 20 ft., one target. Hit: 18 (2d8 + 9) bludgeoning damage.", "attack_bonus": 16, "damage": [{"damage_type": {"index": "bludgeoning", "name": "Bludgeoning", "url": "/api/2014/damage-types/bludgeoning"}, "damage_dice": "2d8+9"}], "actions": []}, {"name": "Frightful Presence", "desc": "Each creature of the dragon's choice that is within 120 feet of the dragon and aware of it must succeed on a DC 20 Wisdom saving throw or become frightened for 1 minute. A creature can repeat the saving throw at the end of each of its turns, ending the effect on itself on a success. If a creature's saving throw is successful or the effect ends for it, the creature is immune to the dragon's Frightful Presence for the next 24 hours.", "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_value": 20, "success_type": "none"}, "damage": [], "actions": []}, {"name": "Lightning Breath", "desc": "The dragon exhales lightning in a 120-foot line that is 10 feet wide. Each creature in that line must make a DC 23 Dexterity saving throw, taking 88 (16d10) lightning damage on a failed save, or half as much damage on a successful one.", "usage": {"type": "recharge on roll", "dice": "1d6", "min_value": 5}, "dc": {"dc_type": {"index": "dex", "name": "DEX", "url": "/api/2014/ability-scores/dex"}, "dc_value": 23, "success_type": "half"}, "damage": [{"damage_type": {"index": "lightning", "name": "Lightning", "url": "/api/2014/damage-types/lightning"}, "damage_dice": "16d10"}], "actions": []}], "legendary_actions": [{"name": "Detect", "desc": "The dragon makes a Wisdom (Perception) check.", "damage": []}, {"name": "Tail Attack", "desc": "The dragon makes a tail attack.", "damage": []}, {"name": "Wing Attack (Costs 2 Actions)", "desc": "The dragon beats its wings. Each creature within 15 ft. of the dragon must succeed on a DC 24 Dexterity saving throw or take 16 (2d6 + 9) bludgeoning damage and be knocked prone. The dragon can then fly up to half its flying speed.", "dc": {"dc_type": {"index": "dex", "name": "DEX", "url": "/api/2014/ability-scores/dex"}, "dc_value": 24, "success_type": "none"}, "damage": [{"damage_type": {"index": "bludgeoning", "name": "Bludgeoning", "url": "/api/2014/damage-types/bludgeoning"}, "damage_dice": "2d6+9"}]}], "image": "/api/images/monsters/ancient-blue-dragon.png", "url": "/api/2014/monsters/ancient-blue-dragon", "updated_at": "2025-05-04T02:15:02.221Z", "forms": [], "reactions": []}