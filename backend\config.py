"""
配置文件，包含所有全局配置参数
"""
import os
from pathlib import Path

# 基础路径
BASE_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
PLAYERS_DIR = BASE_DIR / "players"
DATA_DIR = BASE_DIR / "data"
LOGS_DIR = BASE_DIR / "logs"

# 服务器配置
HOST = "0.0.0.0"
PORT = 8092  # 修改端口避免冲突

# 日志配置
LOG_LEVEL = "DEBUG"  # 可选: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE = LOGS_DIR / "dnd_game.log"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
LOG_TO_CONSOLE = True  # 开发期间是否同时输出到控制台

# 角色创建配置
DEFAULT_PASSWORD = "123456"
INITIAL_RANDOM_POINTS_MIN = 20
INITIAL_RANDOM_POINTS_MAX = 30
ADDITIONAL_POINTS_MIN = 10
ADDITIONAL_POINTS_MAX = 20

# DND游戏相关配置
CHARACTER_ATTRIBUTES = [
    "strength",     # 力量
    "dexterity",    # 敏捷
    "constitution", # 体质
    "intelligence", # 智力
    "wisdom",       # 感知
    "charisma"      # 魅力
]

CHARACTER_RACES = [
    "人类",     # Human
    "精灵",     # Elf
    "矮人",     # Dwarf
    "半身人",   # Halfling
    "半精灵",   # Half-Elf
    "半兽人",   # Half-Orc
    "龙裔",     # Dragonborn
    "侏儒"      # Gnome
]

CHARACTER_CLASSES = [
    "战士",     # Fighter
    "法师",     # Wizard
    "牧师",     # Cleric
    "盗贼",     # Rogue
    "游侠",     # Ranger
    "圣武士",   # Paladin
    "德鲁伊",   # Druid
    "术士",     # Sorcerer
    "warlock",  # Warlock
    "诗人"      # Bard
]

# 最大角色数
MAX_CHARACTERS_PER_ACCOUNT = 5

# LLM配置
LLM_MODEL =  "huihui_ai/mistral-small-abliterated:24b" #"huihui_ai/mistral-small-abliterated:24b"  # "huihui_ai/phi4-abliterated:14b"
OLLAMA_API_URL = "http://localhost:11434"  # Ollama API地址

# 图像生成配置
ENABLE_IMAGE_GENERATION = True  # 是否启用图像生成功能
IMAGE_GENERATION_API = ""  # 图像生成API地址

# ComfyUI 配置
COMFYUI_API_URL = "http://127.0.0.1:8188"  # ComfyUI API地址
COMFYUI_TEMPLATE_FILE = BASE_DIR / "services/comfyui_generator/templates/character_portrait.json"  # 模板文件路径
COMFYUI_OUTPUT_DIR = BASE_DIR.parent / "flutter_frontend/cache_bioimage"  # 图像输出目录

# 配置项字典，用于服务内部获取配置
config = {
    "HOST": HOST,
    "PORT": PORT,
    "LOG_LEVEL": LOG_LEVEL,
    "LOG_FILE": str(LOG_FILE),
    "LOG_FORMAT": LOG_FORMAT,
    "LOG_DATE_FORMAT": LOG_DATE_FORMAT,
    "LOG_TO_CONSOLE": LOG_TO_CONSOLE,
    "DEFAULT_PASSWORD": DEFAULT_PASSWORD,
    "LLM_MODEL": LLM_MODEL,
    "OLLAMA_API_URL": OLLAMA_API_URL,
    "ENABLE_IMAGE_GENERATION": ENABLE_IMAGE_GENERATION,
    "IMAGE_GENERATION_API": IMAGE_GENERATION_API,
    "COMFYUI_API_URL": COMFYUI_API_URL,
    "COMFYUI_TEMPLATE_FILE": str(COMFYUI_TEMPLATE_FILE),
    "COMFYUI_OUTPUT_DIR": str(COMFYUI_OUTPUT_DIR)
} 