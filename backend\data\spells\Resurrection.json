{"higher_level": [], "index": "resurrection", "name": "Resurrection", "desc": ["You touch a dead creature that has been dead for no more than a century, that didn't die of old age, and that isn't undead. If its soul is free and willing, the target returns to life with all its hit points.", "This spell neutralizes any poisons and cures normal diseases afflicting the creature when it died. It doesn't, however, remove magical diseases, curses, and the like; if such effects aren't removed prior to casting the spell, they afflict the target on its return to life.", "This spell closes all mortal wounds and restores any missing body parts.", "Coming back from the dead is an ordeal. The target takes a -4 penalty to all attack rolls, saving throws, and ability checks. Every time the target finishes a long rest, the penalty is reduced by 1 until it disappears.", "Casting this spell to restore life to a creature that has been dead for one year or longer taxes you greatly. Until you finish a long rest, you can't cast spells again, and you have disadvantage on all attack rolls, ability checks, and saving throws."], "range": "Touch", "components": ["V", "S", "M"], "material": "A diamond worth at least 1,000gp, which the spell consumes.", "ritual": false, "duration": "Instantaneous", "concentration": false, "casting_time": "1 hour", "level": 7, "school": {"index": "necromancy", "name": "Necromancy", "url": "/api/2014/magic-schools/necromancy"}, "classes": [{"index": "bard", "name": "Bard", "url": "/api/2014/classes/bard"}, {"index": "cleric", "name": "Cleric", "url": "/api/2014/classes/cleric"}], "subclasses": [], "url": "/api/2014/spells/resurrection", "updated_at": "2025-04-08T21:14:16.147Z"}