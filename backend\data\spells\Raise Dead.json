{"higher_level": [], "index": "raise-dead", "name": "<PERSON><PERSON>", "desc": ["You return a dead creature you touch to life, provided that it has been dead no longer than 10 days. If the creature's soul is both willing and at liberty to rejoin the body, the creature returns to life with 1 hit point.", "This spell also neutralizes any poisons and cures nonmagical diseases that affected the creature at the time it died. This spell doesn't, however, remove magical diseases, curses, or similar effects; if these aren't first removed prior to casting the spell, they take effect when the creature returns to life. The spell can't return an undead creature to life.", "This spell closes all mortal wounds, but it doesn't restore missing body parts. If the creature is lacking body parts or organs integral for its survival--its head, for instance--the spell automatically fails.", "Coming back from the dead is an ordeal. The target takes a -4 penalty to all attack rolls, saving throws, and ability checks. Every time the target finishes a long rest, the penalty is reduced by 1 until it disappears."], "range": "Touch", "components": ["V", "S", "M"], "material": "A diamond worth at least 500gp, which the spell consumes.", "ritual": false, "duration": "Instantaneous", "concentration": false, "casting_time": "1 hour", "level": 5, "school": {"index": "necromancy", "name": "Necromancy", "url": "/api/2014/magic-schools/necromancy"}, "classes": [{"index": "bard", "name": "Bard", "url": "/api/2014/classes/bard"}, {"index": "cleric", "name": "Cleric", "url": "/api/2014/classes/cleric"}, {"index": "paladin", "name": "<PERSON><PERSON><PERSON>", "url": "/api/2014/classes/paladin"}], "subclasses": [{"index": "life", "name": "Life", "url": "/api/2014/subclasses/life"}], "url": "/api/2014/spells/raise-dead", "updated_at": "2025-04-08T21:14:16.147Z"}