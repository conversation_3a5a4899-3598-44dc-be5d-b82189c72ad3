import 'package:flutter/foundation.dart';
import '../models/character.dart';
import '../services/character_service.dart';

/// 角色状态管理
class CharacterProvider with ChangeNotifier {
  final CharacterService _characterService = CharacterService();
  
  List<CharacterSummary> _characters = [];
  CharacterSummary? _selectedCharacter;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<CharacterSummary> get characters => _characters;
  CharacterSummary? get selectedCharacter => _selectedCharacter;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// 获取角色列表
  Future<void> loadCharacters(String username) async {
    _setLoading(true);
    _clearError();

    try {
      final characters = await _characterService.getCharacterList(username);
      _characters = characters;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 创建角色
  Future<bool> createCharacter(CreateCharacterRequest request) async {
    _setLoading(true);
    _clearError();

    try {
      final newCharacter = await _characterService.createCharacter(request);
      _characters.add(new<PERSON>haracter);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 删除角色
  Future<bool> deleteCharacter(String username, String characterId) async {
    _setLoading(true);
    _clearError();

    try {
      await _characterService.deleteCharacter(username, characterId);
      _characters.removeWhere((char) => char.id == characterId);
      
      // 如果删除的是当前选中的角色，清除选择
      if (_selectedCharacter?.id == characterId) {
        _selectedCharacter = null;
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 选择角色
  void selectCharacter(CharacterSummary character) {
    _selectedCharacter = character;
    notifyListeners();
  }

  /// 清除选择的角色
  void clearSelectedCharacter() {
    _selectedCharacter = null;
    notifyListeners();
  }

  /// 随机生成角色属性
  CharacterAttributes generateRandomAttributes() {
    return _characterService.generateRandomAttributes();
  }

  /// 计算派生属性
  DerivedStats calculateDerivedStats(
    CharacterAttributes attributes,
    String characterClass,
  ) {
    return _characterService.calculateDerivedStats(attributes, characterClass);
  }

  /// 计算技能值
  Skills calculateSkills(String characterClass, String race) {
    return _characterService.calculateSkills(characterClass, race);
  }

  /// 应用种族属性加成
  CharacterAttributes applyRacialBonus(
    CharacterAttributes attributes,
    String race,
  ) {
    return _characterService.applyRacialBonus(attributes, race);
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// 清除错误（供UI调用）
  void clearError() {
    _clearError();
  }

  /// 重置状态
  void reset() {
    _characters = [];
    _selectedCharacter = null;
    _isLoading = false;
    _error = null;
    notifyListeners();
  }
}
