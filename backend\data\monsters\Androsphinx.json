{"index": "androsphinx", "name": "<PERSON><PERSON><PERSON><PERSON>", "size": "Large", "type": "monstrosity", "alignment": "lawful neutral", "armor_class": [{"type": "natural", "value": 17}], "hit_points": 199, "hit_dice": "19d10", "hit_points_roll": "19d10+95", "speed": {"walk": "40 ft.", "fly": "60 ft."}, "strength": 22, "dexterity": 10, "constitution": 20, "intelligence": 16, "wisdom": 18, "charisma": 23, "proficiencies": [{"value": 6, "proficiency": {"index": "saving-throw-dex", "name": "Saving Throw: DEX", "url": "/api/2014/proficiencies/saving-throw-dex"}}, {"value": 11, "proficiency": {"index": "saving-throw-con", "name": "Saving Throw: CON", "url": "/api/2014/proficiencies/saving-throw-con"}}, {"value": 9, "proficiency": {"index": "saving-throw-int", "name": "Saving Throw: INT", "url": "/api/2014/proficiencies/saving-throw-int"}}, {"value": 10, "proficiency": {"index": "saving-throw-wis", "name": "Saving Throw: WIS", "url": "/api/2014/proficiencies/saving-throw-wis"}}, {"value": 9, "proficiency": {"index": "skill-arcana", "name": "Skill: <PERSON>ana", "url": "/api/2014/proficiencies/skill-arcana"}}, {"value": 10, "proficiency": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"value": 15, "proficiency": {"index": "skill-religion", "name": "Skill: Religion", "url": "/api/2014/proficiencies/skill-religion"}}], "damage_vulnerabilities": [], "damage_resistances": [], "damage_immunities": ["psychic", "bludgeoning, piercing, and slashing from nonmagical weapons"], "condition_immunities": [{"index": "charmed", "name": "Charmed", "url": "/api/2014/conditions/charmed"}, {"index": "frightened", "name": "Frightened", "url": "/api/2014/conditions/frightened"}], "senses": {"truesight": "120 ft.", "passive_perception": 20}, "languages": "Common, Sphinx", "challenge_rating": 17, "proficiency_bonus": 6, "xp": 18000, "special_abilities": [{"name": "Inscrutable", "desc": "The sphinx is immune to any effect that would sense its emotions or read its thoughts, as well as any divination spell that it refuses. Wisdom (Insight) checks made to ascertain the sphinx's intentions or sincerity have disadvantage.", "damage": []}, {"name": "Magic Weapons", "desc": "The sphinx's weapon attacks are magical.", "damage": []}, {"name": "Spellcasting", "desc": "The sphinx is a 12th-level spellcaster. Its spellcasting ability is Wisdom (spell save DC 18, +10 to hit with spell attacks). It requires no material components to cast its spells. The sphinx has the following cleric spells prepared:\n\n- Cantrips (at will): sacred flame, spare the dying, thaumaturgy\n- 1st level (4 slots): command, detect evil and good, detect magic\n- 2nd level (3 slots): lesser restoration, zone of truth\n- 3rd level (3 slots): dispel magic, tongues\n- 4th level (3 slots): banishment, freedom of movement\n- 5th level (2 slots): flame strike, greater restoration\n- 6th level (1 slot): heroes' feast", "spellcasting": {"level": 12, "ability": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc": 18, "modifier": 10, "components_required": ["V", "S"], "school": "cleric", "slots": {"1": 4, "2": 3, "3": 3, "4": 3, "5": 2, "6": 1}, "spells": [{"name": "Sacred Flame", "level": 0, "url": "/api/2014/spells/sacred-flame"}, {"name": "Spare the Dying", "level": 0, "url": "/api/2014/spells/spare-the-dying"}, {"name": "Thaumaturgy", "level": 0, "url": "/api/2014/spells/thaumaturgy"}, {"name": "Command", "level": 1, "url": "/api/2014/spells/command"}, {"name": "Detect Evil and Good", "level": 1, "url": "/api/2014/spells/detect-evil-and-good"}, {"name": "Detect Magic", "level": 1, "url": "/api/2014/spells/detect-magic"}, {"name": "Lesser Restoration", "level": 2, "url": "/api/2014/spells/lesser-restoration"}, {"name": "Zone of Truth", "level": 2, "url": "/api/2014/spells/zone-of-truth"}, {"name": "Dispel Magic", "level": 3, "url": "/api/2014/spells/dispel-magic"}, {"name": "Tongues", "level": 3, "url": "/api/2014/spells/tongues"}, {"name": "Banishment", "level": 4, "url": "/api/2014/spells/banishment"}, {"name": "Freedom of Movement", "level": 4, "url": "/api/2014/spells/freedom-of-movement"}, {"name": "Flame Strike", "level": 5, "url": "/api/2014/spells/flame-strike"}, {"name": "Greater Restoration", "level": 5, "url": "/api/2014/spells/greater-restoration"}, {"name": "Heroes' Feast", "level": 6, "url": "/api/2014/spells/heroes-feast"}]}, "damage": []}], "actions": [{"name": "Multiattack", "multiattack_type": "actions", "desc": "The sphinx makes two claw attacks.", "actions": [{"action_name": "Claw", "count": "2", "type": "melee"}], "damage": []}, {"name": "Claw", "desc": "Melee Weapon Attack: +12 to hit, reach 5 ft., one target. Hit: 17 (2d10 + 6) slashing damage.", "attack_bonus": 12, "damage": [{"damage_type": {"index": "slashing", "name": "Slashing", "url": "/api/2014/damage-types/slashing"}, "damage_dice": "2d10+6"}], "actions": []}, {"name": "Roar", "desc": "The sphinx emits a magical roar. Each time it roars before finishing a long rest, the roar is louder and the effect is different, as detailed below. Each creature within 500 feet of the sphinx and able to hear the roar must make a saving throw.\n\nFirst Roar. Each creature that fails a DC 18 Wisdom saving throw is frightened for 1 minute. A frightened creature can repeat the saving throw at the end of each of its turns, ending the effect on itself on a success.\n\nSecond Roar. Each creature that fails a DC 18 Wisdom saving throw is deafened and frightened for 1 minute. A frightened creature is paralyzed and can repeat the saving throw at the end of each of its turns, ending the effect on itself on a success.\n\nThird Roar. Each creature makes a DC 18 Constitution saving throw. On a failed save, a creature takes 44 (8d10) thunder damage and is knocked prone. On a successful save, the creature takes half as much damage and isn't knocked prone.", "usage": {"type": "per day", "times": 3}, "attacks": [{"name": "First Roar", "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_value": 18, "success_type": "none"}}, {"name": "Second Roar", "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_value": 18, "success_type": "none"}}, {"name": "Third Roar", "dc": {"dc_type": {"index": "con", "name": "CON", "url": "/api/2014/ability-scores/con"}, "dc_value": 18, "success_type": "half"}, "damage": [{"damage_type": {"index": "thunder", "name": "Thunder", "url": "/api/2014/damage-types/thunder"}, "damage_dice": "8d10"}]}], "damage": [], "actions": []}], "legendary_actions": [{"name": "Claw Attack", "desc": "The sphinx makes one claw attack.", "damage": []}, {"name": "Teleport (Costs 2 Actions)", "desc": "The sphinx magically teleports, along with any equipment it is wearing or carrying, up to 120 feet to an unoccupied space it can see.", "damage": []}, {"name": "Cast a Spell (Costs 3 Actions)", "desc": "The sphinx casts a spell from its list of prepared spells, using a spell slot as normal.", "damage": []}], "image": "/api/images/monsters/androsphinx.png", "url": "/api/2014/monsters/androsphinx", "updated_at": "2025-05-04T02:15:02.222Z", "forms": [], "reactions": []}