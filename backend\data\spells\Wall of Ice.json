{"index": "wall-of-ice", "name": "Wall of Ice", "desc": ["You create a wall of ice on a solid surface within range. You can form it into a hemispherical dome or a sphere with a radius of up to 10 feet, or you can shape a flat surface made up of ten 10-foot-square panels. Each panel must be contiguous with another panel. In any form, the wall is 1 foot thick and lasts for the duration.", "If the wall cuts through a creature's space when it appears, the creature within its area is pushed to one side of the wall and must make a dexterity saving throw. On a failed save, the creature takes 10d6 cold damage, or half as much damage on a successful save.", "The wall is an object that can be damaged and thus breached. It has AC 12 and 30 hit points per 10-foot section, and it is vulnerable to fire damage. Reducing a 10-foot section of wall to 0 hit points destroys it and leaves behind a sheet of frigid air in the space the wall occupied. A creature moving through the sheet of frigid air for the first time on a turn must make a constitution saving throw. That creature takes 5d6 cold damage on a failed save, or half as much damage on a successful one."], "higher_level": ["When you cast this spell using a spell slot of 7th level or higher, the damage the wall deals when it appears increases by 2d6, and the damage from passing through the sheet of frigid air increases by 1d6, for each slot level above 6th."], "range": "120 feet", "components": ["V", "S", "M"], "material": "A small piece of quartz.", "ritual": false, "duration": "Up to 10 minutes", "concentration": true, "casting_time": "1 action", "level": 6, "damage": {"damage_type": {"index": "cold", "name": "Cold", "url": "/api/2014/damage-types/cold"}, "damage_at_slot_level": {"6": "10d6", "7": "12d6", "8": "14d6", "9": "16d6"}}, "dc": {"dc_type": {"index": "dex", "name": "DEX", "url": "/api/2014/ability-scores/dex"}, "dc_success": "half"}, "area_of_effect": {"type": "sphere", "size": 10}, "school": {"index": "evocation", "name": "Evocation", "url": "/api/2014/magic-schools/evocation"}, "classes": [{"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [], "url": "/api/2014/spells/wall-of-ice", "updated_at": "2025-04-08T21:14:16.147Z"}