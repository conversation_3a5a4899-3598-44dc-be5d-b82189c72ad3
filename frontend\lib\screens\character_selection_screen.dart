import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/character_provider.dart';
import '../widgets/character_card.dart';
import '../widgets/custom_button.dart';
import '../widgets/loading_dialog.dart';
import '../utils/constants.dart';
import '../utils/debug_logger.dart';
import 'character_creation_screen.dart';
import 'game_screen.dart';
import 'login_screen.dart';

/// 角色选择界面
class CharacterSelectionScreen extends StatefulWidget {
  const CharacterSelectionScreen({Key? key}) : super(key: key);

  @override
  State<CharacterSelectionScreen> createState() => _CharacterSelectionScreenState();
}

class _CharacterSelectionScreenState extends State<CharacterSelectionScreen> {
  @override
  void initState() {
    super.initState();
    DebugLogger.info('角色选择界面初始化', tag: 'CHARACTER_SELECTION');
    _loadCharacters();
  }

  /// 加载角色列表
  void _loadCharacters() async {
    DebugLogger.info('开始加载角色列表', tag: 'CHARACTER_SELECTION');
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final characterProvider = Provider.of<CharacterProvider>(context, listen: false);

    if (authProvider.user != null) {
      DebugLogger.debug('用户已登录，用户名: ${authProvider.user!.username}', tag: 'CHARACTER_SELECTION');
      await characterProvider.loadCharacters(authProvider.user!.username);

      DebugLogger.debug('角色加载完成，角色数量: ${characterProvider.characters.length}', tag: 'CHARACTER_SELECTION');

      // 如果有错误，显示错误信息
      if (characterProvider.error != null) {
        DebugLogger.error('加载角色列表失败: ${characterProvider.error}', tag: 'CHARACTER_SELECTION');
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('错误'),
              content: Text(characterProvider.error!),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    characterProvider.clearError();
                  },
                  child: const Text('确定'),
                ),
              ],
            ),
          );
        }
      }
    } else {
      DebugLogger.warning('用户未登录，无法加载角色列表', tag: 'CHARACTER_SELECTION');
    }
  }

  /// 创建角色
  void _createCharacter() {
    DebugLogger.userAction('点击创建角色按钮', tag: 'CHARACTER_SELECTION');
    DebugLogger.navigation('CharacterSelectionScreen', 'CharacterCreationScreen');

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CharacterCreationScreen(),
      ),
    ).then((_) {
      // 从角色创建界面返回后，重新加载角色列表
      DebugLogger.debug('从角色创建界面返回，重新加载角色列表', tag: 'CHARACTER_SELECTION');
      _loadCharacters();
    });
  }

  /// 删除角色
  void _deleteCharacter(String characterId) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final characterProvider = Provider.of<CharacterProvider>(context, listen: false);
    
    if (authProvider.user != null) {
      final success = await characterProvider.deleteCharacter(
        authProvider.user!.username,
        characterId,
      );
      
      if (success) {
        // 更新认证提供者中的角色列表
        authProvider.updateUserCharacters(characterProvider.characters);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('角色删除成功')),
          );
        }
      } else if (characterProvider.error != null) {
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('错误'),
              content: Text(characterProvider.error!),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    characterProvider.clearError();
                  },
                  child: const Text('确定'),
                ),
              ],
            ),
          );
        }
      }
    }
  }

  /// 进入游戏
  void _enterGame() async {
    final characterProvider = Provider.of<CharacterProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (characterProvider.selectedCharacter != null) {
      DebugLogger.userAction('点击进入游戏按钮', params: {
        'character_id': characterProvider.selectedCharacter!.id,
        'character_name': characterProvider.selectedCharacter!.name,
      });

      // 保存最后选择的角色ID
      await authProvider.saveLastCharacterId(
        characterProvider.selectedCharacter!.id,
      );

      DebugLogger.navigation('CharacterSelectionScreen', 'GameScreen');

      // 跳转到游戏界面
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const GameScreen(),
          ),
        );
      }
    } else {
      DebugLogger.warning('没有选择角色，无法进入游戏', tag: 'CHARACTER_SELECTION');
    }
  }

  /// 登出
  void _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final characterProvider = Provider.of<CharacterProvider>(context, listen: false);
      
      await authProvider.logout();
      characterProvider.reset();
      
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const LoginScreen(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('角色选择'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: _logout,
            icon: const Icon(Icons.logout),
            tooltip: '退出登录',
          ),
        ],
      ),
      body: Consumer2<AuthProvider, CharacterProvider>(
        builder: (context, authProvider, characterProvider, child) {
          if (characterProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // 用户信息
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                child: Text(
                  '欢迎回来，${authProvider.user?.username ?? ''}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // 角色列表
              Expanded(
                child: characterProvider.characters.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        itemCount: characterProvider.characters.length,
                        itemBuilder: (context, index) {
                          final character = characterProvider.characters[index];
                          return CharacterCard(
                            character: character,
                            isSelected: characterProvider.selectedCharacter?.id == character.id,
                            onTap: () => characterProvider.selectCharacter(character),
                            onDelete: () => _deleteCharacter(character.id),
                          );
                        },
                      ),
              ),

              // 操作按钮
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // 创建角色按钮
                    CustomButton(
                      text: '创建角色',
                      onPressed: characterProvider.characters.length < AppConstants.maxCharactersPerAccount
                          ? _createCharacter
                          : null,
                      width: double.infinity,
                    ),
                    const SizedBox(height: 12),

                    // 进入游戏按钮
                    CustomButton(
                      text: '进入游戏',
                      onPressed: characterProvider.selectedCharacter != null
                          ? _enterGame
                          : null,
                      width: double.infinity,
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建空状态界面
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_add,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '还没有角色',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击下方按钮创建你的第一个角色',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}
