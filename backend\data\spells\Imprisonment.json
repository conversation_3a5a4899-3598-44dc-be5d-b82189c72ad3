{"higher_level": [], "index": "imprisonment", "name": "Imprisonment", "desc": ["You create a magical restraint to hold a creature that you can see within range. The target must succeed on a wisdom saving throw or be bound by the spell; if it succeeds, it is immune to this spell if you cast it again. While affected by this spell, the creature doesn't need to breathe, eat, or drink, and it doesn't age. Divination spells can't locate or perceive the target.", "When you cast the spell, you choose one of the following forms of imprisonment.", "***Burial.*** The target is entombed far beneath the earth in a sphere of magical force that is just large enough to contain the target. Nothing can pass through the sphere, nor can any creature teleport or use planar travel to get into or out of it.", "The special component for this version of the spell is a small mithral orb.", "***Chaining.*** Heavy chains, firmly rooted in the ground, hold the target in place. The target is restrained until the spell ends, and it can't move or be moved by any means until then.", "The special component for this version of the spell is a fine chain of precious metal.", "***Hedged Prison.*** The spell transports the target into a tiny demiplane that is warded against teleportation and planar travel. The demiplane can be a labyrinth, a cage, a tower, or any similar confined structure or area of your choice.", "The special component for this version of the spell is a miniature representation of the prison made from jade.", "***Minimus Containment.*** The target shrinks to a height of 1 inch and is imprisoned inside a gemstone or similar object. Light can pass through the gemstone normally (allowing the target to see out and other creatures to see in), but nothing else can pass through, even by means of teleportation or planar travel. The gemstone can't be cut or broken while the spell remains in effect.", "The special component for this version of the spell is a large, transparent gemstone, such as a corundum, diamond, or ruby.", "***Slumber.*** The target falls asleep and can't be awoken.", "The special component for this version of the spell consists of rare soporific herbs.", "***Ending the Spell.*** During the casting of the spell, in any of its versions, you can specify a condition that will cause the spell to end and release the target. The condition can be as specific or as elaborate as you choose, but the GM must agree that the condition is reasonable and has a likelihood of coming to pass. The conditions can be based on a creature's name, identity, or deity but otherwise must be based on observable actions or qualities and not based on intangibles such as level, class, or hit points.", "A dispel magic spell can end the spell only if it is cast as a 9th-level spell, targeting either the prison or the special component used to create it.", "You can use a particular special component to create only one prison at a time. If you cast the spell again using the same component, the target of the first casting is immediately freed from its binding."], "range": "30 feet", "components": ["V", "S", "M"], "material": "A vellum depiction or a carved statuette in the likeness of the target, and a special component that varies according to the version of the spell you choose, worth at least 500gp per Hit Die of the target.", "ritual": false, "duration": "Until dispelled", "concentration": false, "casting_time": "1 minute", "level": 9, "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_success": "none", "desc": "If the creature succeeds, it is immune to this spell if you cast it again"}, "school": {"index": "abjuration", "name": "Abjuration", "url": "/api/2014/magic-schools/abjuration"}, "classes": [{"index": "warlock", "name": "<PERSON><PERSON>", "url": "/api/2014/classes/warlock"}, {"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [], "url": "/api/2014/spells/imprisonment", "updated_at": "2025-04-08T21:14:16.147Z"}