{"higher_level": [], "index": "suggestion", "name": "Suggestion", "desc": ["You suggest a course of activity (limited to a sentence or two) and magically influence a creature you can see within range that can hear and understand you. Creatures that can't be charmed are immune to this effect. The suggestion must be worded in such a manner as to make the course of action sound reasonable. Asking the creature to stab itself, throw itself onto a spear, immolate itself, or do some other obviously harmful act ends the spell.", "The target must make a wisdom saving throw. On a failed save, it pursues the course of action you described to the best of its ability. The suggested course of action can continue for the entire duration. If the suggested activity can be completed in a shorter time, the spell ends when the subject finishes what it was asked to do.", "You can also specify conditions that will trigger a special activity during the duration. For example, you might suggest that a knight give her warhorse to the first beggar she meets. If the condition isn't met before the spell expires, the activity isn't performed.", "If you or any of your companions damage the target, the spell ends."], "range": "30 feet", "components": ["V", "M"], "material": "A snake's tongue and either a bit of honeycomb or a drop of sweet oil.", "ritual": false, "duration": "Up to 8 hours", "concentration": true, "casting_time": "1 action", "level": 2, "dc": {"dc_type": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "dc_success": "none"}, "school": {"index": "enchantment", "name": "Enchantment", "url": "/api/2014/magic-schools/enchantment"}, "classes": [{"index": "bard", "name": "Bard", "url": "/api/2014/classes/bard"}, {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}, {"index": "warlock", "name": "<PERSON><PERSON>", "url": "/api/2014/classes/warlock"}, {"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [{"index": "lore", "name": "Lore", "url": "/api/2014/subclasses/lore"}], "url": "/api/2014/spells/suggestion", "updated_at": "2025-04-08T21:14:16.147Z"}