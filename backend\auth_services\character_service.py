"""
角色服务，负责处理角色创建、删除和查询
"""
import os
import json
import random
import datetime
from pathlib import Path
from typing import Tuple, List, Dict, Optional

from models import Character, CharacterAttributes, DerivedStats, Skills, BasicInfo, Inventory, Equipment
from ..config import (
    PLAYERS_DIR, CHARACTER_RACES, CHARACTER_CLASSES,
    MAX_CHARACTERS_PER_ACCOUNT
)
from ..logger import debug, error, info

class CharacterService:
    @staticmethod
    def create_character(
        username: str, 
        character_name: str,
        race: str,
        character_class: str,
        attributes: CharacterAttributes,
        gender: str = "男",
        age: int = 20,
        derived_stats: Optional[DerivedStats] = None,
        skills: Optional[Skills] = None,
        currency: int = 0
    ) -> str:
        """
        创建新角色
        
        Args:
            username: 用户名
            character_name: 角色名
            race: 种族
            character_class: 职业
            attributes: 属性值
            gender: 性别
            age: 年龄
            derived_stats: 派生属性
            skills: 技能
            currency: 货币
            
        Returns:
            str: 创建的角色ID
        """
        debug(f"创建角色: {username}/{character_name}, 种族: {race}, 职业: {character_class}")
        
        # 验证种族和职业
        if race not in CHARACTER_RACES:
            raise ValueError(f"无效的种族，可选种族: {', '.join(CHARACTER_RACES)}")
        
        if character_class not in CHARACTER_CLASSES:
            raise ValueError(f"无效的职业，可选职业: {', '.join(CHARACTER_CLASSES)}")
        
        # 检查用户目录
        user_dir = PLAYERS_DIR / username
        if not user_dir.exists():
            raise ValueError("用户不存在")
        
        # 检查角色数量限制
        chars_dir = user_dir / "characters"
        if chars_dir.exists():
            char_count = len(list(chars_dir.glob("*.json")))
            if char_count >= MAX_CHARACTERS_PER_ACCOUNT:
                raise ValueError(f"每个账户最多创建{MAX_CHARACTERS_PER_ACCOUNT}个角色")
        else:
            chars_dir.mkdir(exist_ok=True)
        
        # 使用传入的attributes（确保为CharacterAttributes类型）
        debug(f"角色属性: {attributes}")
        
        # 处理种族加成
        if race == "矮人":
            attributes.STR += 2
        elif race == "精灵":
            attributes.MIND += 2
        elif race == "半身人":
            attributes.DEX += 2
        
        # 创建基础信息
        basic_info = BasicInfo(**{
            'name': character_name,
            'player': username,
            'race': race,
            'class': character_class,  # 使用class字段名
            'level': 1,
            'exp': 0,
            'age': age,
            'gender': gender
        })

        # 创建角色
        character = Character(
            basic=basic_info,
            inventory=Inventory(currency=currency),
            equipment=Equipment(),
            attributes=attributes,
            created_at=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        # 如果提供了派生属性和技能，使用提供的值
        if derived_stats:
            character.derived_stats = derived_stats
        if skills:
            character.skills = skills
        if currency > 0:
            character.currency = currency
        
        # 计算派生属性
        character.calculate_derived_stats()
        
        # 保存角色数据
        char_file = chars_dir / f"{character.id}.json"
        with open(char_file, "w", encoding="utf-8") as f:
            f.write(character.model_dump_json(indent=4, exclude_none=True))
        
        return character.id
    
    @staticmethod
    def delete_character(username: str, character_id: str) -> None:
        """
        删除角色
        
        Args:
            username: 用户名
            character_id: 角色ID
        """
        user_dir = PLAYERS_DIR / username
        if not user_dir.exists():
            raise ValueError("用户不存在")
        
        char_file = user_dir / "characters" / f"{character_id}.json"
        if not char_file.exists():
            raise ValueError("角色不存在")
        
        os.remove(char_file)
    
    @staticmethod
    def get_character(username: str, character_id: str) -> Character:
        """
        获取角色详情
        
        Args:
            username: 用户名
            character_id: 角色ID
            
        Returns:
            Character: 角色对象
        """
        user_dir = PLAYERS_DIR / username
        if not user_dir.exists():
            raise ValueError("用户不存在")
        
        char_file = user_dir / "characters" / f"{character_id}.json"
        if not char_file.exists():
            raise ValueError("角色不存在")
        
        try:
            with open(char_file, "r", encoding="utf-8") as f:
                char_data = json.load(f)
            
            return Character.parse_obj(char_data)
        except Exception as e:
            error(f"读取角色数据失败: {str(e)}")
            raise ValueError(f"读取角色数据失败: {str(e)}")
    
    @staticmethod
    def generate_random_attributes() -> CharacterAttributes:
        """
        生成随机角色属性 - 使用3d6生成
        
        Returns:
            CharacterAttributes: 随机生成的属性
        """
        # 使用3d6生成随机属性
        attributes = {
            "STR": CharacterService.roll_3d6(),
            "DEX": CharacterService.roll_3d6(),
            "MIND": CharacterService.roll_3d6()
        }
        
        return CharacterAttributes(**attributes)
    
    @staticmethod
    def roll_3d6() -> int:
        """
        掷3d6
        
        Returns:
            int: 掷骰结果
        """
        return random.randint(1, 6) + random.randint(1, 6) + random.randint(1, 6)
    
    @staticmethod
    def calculate_modifier(value: int) -> int:
        """
        计算属性修正值
        
        Args:
            value: 属性值
            
        Returns:
            int: 修正值
        """
        return (value - 10) // 2

    def calculate_character_stats(self, username: str, character_id: str) -> Dict:
        """
        计算角色衍生属性
        
        Args:
            username: 用户名
            character_id: 角色ID
            
        Returns:
            Dict: 角色衍生属性
        """
        # 获取角色
        character = self.get_character(username, character_id)
        
        # 计算属性修正值
        str_mod = self.calculate_modifier(character.attributes.STR)
        dex_mod = self.calculate_modifier(character.attributes.DEX)
        mind_mod = self.calculate_modifier(character.attributes.MIND)
        
        # 计算攻击加成
        melee_attack = str_mod + character.level
        ranged_attack = dex_mod + character.level
        magic_attack = mind_mod + character.level
        
        # 计算豁免检定
        fortitude = character.skills.Physical.base + str_mod
        reflex = character.skills.Physical.base + dex_mod
        will = mind_mod + character.level
        
        # 返回计算结果
        return {
            "melee_attack": melee_attack,
            "ranged_attack": ranged_attack,
            "magic_attack": magic_attack,
            "ac": character.derived_stats.ac,
            "hp": character.derived_stats.hp,
            "hp_max": character.derived_stats.hp_max,
            "saves": {
                "fortitude": fortitude,
                "reflex": reflex,
                "will": will
            },
            "skills": {
                "Physical": character.skills.Physical.base + character.skills.Physical.class_bonus + character.skills.Physical.race_bonus,
                "Subterfuge": character.skills.Subterfuge.base + character.skills.Subterfuge.class_bonus + character.skills.Subterfuge.race_bonus,
                "Knowledge": character.skills.Knowledge.base + character.skills.Knowledge.class_bonus + character.skills.Knowledge.race_bonus,
                "Communication": character.skills.Communication.base + character.skills.Communication.class_bonus + character.skills.Communication.race_bonus
            }
        } 