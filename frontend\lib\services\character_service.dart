import 'dart:math';
import '../models/api_response.dart';
import '../models/character.dart';
import '../utils/api_config.dart';
import '../utils/constants.dart';
import '../utils/debug_logger.dart';
import 'api_service.dart';

/// 角色服务类
class CharacterService {
  static final CharacterService _instance = CharacterService._internal();
  factory CharacterService() => _instance;
  CharacterService._internal();

  final ApiService _apiService = ApiService();
  final Random _random = Random();

  /// 创建角色
  Future<CharacterSummary> createCharacter(CreateCharacterRequest request) async {
    final response = await _apiService.post(
      ApiConfig.createCharacterEndpoint,
      request.toJson(),
    );

    // 解析响应
    final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
      response,
      (json) => json as Map<String, dynamic>,
    );

    if (!apiResponse.success || apiResponse.data == null) {
      throw Exception(apiResponse.message);
    }

    // 从返回的完整角色数据中提取摘要信息
    final characterData = apiResponse.data!;
    DebugLogger.debug('角色创建响应数据: $characterData', tag: 'CHARACTER_SERVICE');

    return CharacterSummary.fromJson(characterData);
  }

  /// 删除角色
  Future<void> deleteCharacter(String username, String characterId) async {
    final deleteRequest = DeleteCharacterRequest(
      username: username,
      characterId: characterId,
    );

    final response = await _apiService.delete(
      ApiConfig.deleteCharacterEndpoint,
      data: deleteRequest.toJson(),
    );

    // 解析响应
    final apiResponse = ApiResponse<dynamic>.fromJson(
      response,
      (json) => json,
    );

    if (!apiResponse.success) {
      throw Exception(apiResponse.message);
    }
  }

  /// 获取角色列表
  Future<List<CharacterSummary>> getCharacterList(String username) async {
    final response = await _apiService.get(
      ApiConfig.getCharacterListEndpoint(username),
    );

    // 解析响应
    final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
      response,
      (json) => json as Map<String, dynamic>,
    );

    if (!apiResponse.success || apiResponse.data == null) {
      throw Exception(apiResponse.message);
    }

    final charactersData = apiResponse.data!['characters'] as List;
    DebugLogger.debug('角色列表数据: $charactersData', tag: 'CHARACTER_SERVICE');

    return charactersData
        .map((json) => CharacterSummary.fromJson(json))
        .toList();
  }

  /// 随机生成角色属性
  CharacterAttributes generateRandomAttributes() {
    return CharacterAttributes(
      strength: _rollAttribute(),
      dexterity: _rollAttribute(),
      mind: _rollAttribute(),
    );
  }

  /// 掷骰子生成属性值 (3d6)
  int _rollAttribute() {
    int total = 0;
    for (int i = 0; i < 3; i++) {
      total += _random.nextInt(6) + 1;
    }
    return total;
  }

  /// 计算派生属性
  DerivedStats calculateDerivedStats(
    CharacterAttributes attributes,
    String characterClass,
  ) {
    // 计算基础HP (基于力量)
    final baseHp = attributes.strength;
    
    // 计算HP奖励 (1d6)
    final hpBonus = _random.nextInt(6) + 1;
    
    final totalHp = baseHp + hpBonus;

    // 计算AC (10 + 敏捷修正值)
    final dexModifier = (attributes.dexterity - 10) ~/ 2;
    final ac = 10 + dexModifier;

    return DerivedStats(
      hp: totalHp,
      hpBreakdown: HpBreakdown(
        base: baseHp,
        bonus: hpBonus,
      ),
      ac: ac,
      acBreakdown: AcBreakdown(
        base: 10,
        dex: dexModifier,
        armor: 0,
        shield: 0,
        misc: 0,
      ),
    );
  }

  /// 计算技能值
  Skills calculateSkills(String characterClass, String race) {
    // 基础技能等级
    const baseLevel = 1;
    
    // 职业加成
    Map<String, int> classBonus = _getClassBonus(characterClass);
    
    // 种族加成
    Map<String, int> raceBonus = _getRaceBonus(race);

    return Skills(
      physical: SkillDetail(
        base: baseLevel,
        classBonus: classBonus['Physical'] ?? 0,
        raceBonus: raceBonus['Physical'] ?? 0,
      ),
      subterfuge: SkillDetail(
        base: baseLevel,
        classBonus: classBonus['Subterfuge'] ?? 0,
        raceBonus: raceBonus['Subterfuge'] ?? 0,
      ),
      knowledge: SkillDetail(
        base: baseLevel,
        classBonus: classBonus['Knowledge'] ?? 0,
        raceBonus: raceBonus['Knowledge'] ?? 0,
      ),
      communication: SkillDetail(
        base: baseLevel,
        classBonus: classBonus['Communication'] ?? 0,
        raceBonus: raceBonus['Communication'] ?? 0,
      ),
    );
  }

  /// 获取职业技能加成
  Map<String, int> _getClassBonus(String characterClass) {
    switch (characterClass) {
      case '战士':
        return {'Physical': 3};
      case '法师':
        return {'Knowledge': 3};
      case '盗贼':
        return {'Subterfuge': 3};
      case '牧师':
        return {'Communication': 3};
      default:
        return {};
    }
  }

  /// 获取种族技能加成
  Map<String, int> _getRaceBonus(String race) {
    // 所有种族都有+1的通用加成
    return {
      'Physical': 1,
      'Subterfuge': 1,
      'Knowledge': 1,
      'Communication': 1,
    };
  }

  /// 应用种族属性加成
  CharacterAttributes applyRacialBonus(
    CharacterAttributes attributes,
    String race,
  ) {
    switch (race) {
      case '矮人':
        return CharacterAttributes(
          strength: attributes.strength + 2,
          dexterity: attributes.dexterity,
          mind: attributes.mind,
        );
      case '精灵':
        return CharacterAttributes(
          strength: attributes.strength,
          dexterity: attributes.dexterity,
          mind: attributes.mind + 2,
        );
      case '半身人':
        return CharacterAttributes(
          strength: attributes.strength,
          dexterity: attributes.dexterity + 2,
          mind: attributes.mind,
        );
      default: // 人类
        return attributes;
    }
  }
}
