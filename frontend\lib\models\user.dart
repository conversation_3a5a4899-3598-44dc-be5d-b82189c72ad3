import 'package:json_annotation/json_annotation.dart';
import 'character.dart';

part 'user.g.dart';

/// 用户模型
@JsonSerializable()
class User {
  final String username;
  final List<CharacterSummary> characters;

  User({
    required this.username,
    required this.characters,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);

  /// 复制并更新角色列表
  User copyWith({
    String? username,
    List<CharacterSummary>? characters,
  }) {
    return User(
      username: username ?? this.username,
      characters: characters ?? this.characters,
    );
  }
}

/// 账户信息模型
@JsonSerializable()
class Account {
  final String username;
  final String password;
  @JsonKey(name: 'created_at')
  final String? createdAt;

  Account({
    required this.username,
    required this.password,
    this.createdAt,
  });

  factory Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);

  Map<String, dynamic> toJson() => _$AccountToJson(this);
}
