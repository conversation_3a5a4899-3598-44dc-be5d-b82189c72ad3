import 'package:json_annotation/json_annotation.dart';
import 'character.dart';

part 'api_response.g.dart';

/// API响应基础模型
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  /// 创建成功响应
  factory ApiResponse.success(String message, {T? data}) {
    return ApiResponse(
      success: true,
      message: message,
      data: data,
    );
  }

  /// 创建失败响应
  factory ApiResponse.error(String message) {
    return ApiResponse(
      success: false,
      message: message,
      data: null,
    );
  }
}

/// 登录请求模型
@JsonSerializable()
class LoginRequest {
  final String username;
  final String password;

  LoginRequest({
    required this.username,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

/// 登录响应数据模型
@JsonSerializable()
class LoginResponseData {
  final String username;
  final List<CharacterSummary> characters;

  LoginResponseData({
    required this.username,
    required this.characters,
  });

  factory LoginResponseData.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseDataToJson(this);
}


