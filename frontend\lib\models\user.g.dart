// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      username: json['username'] as String,
      characters: (json['characters'] as List<dynamic>)
          .map((e) => CharacterSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'username': instance.username,
      'characters': instance.characters,
    };

Account _$AccountFromJson(Map<String, dynamic> json) => Account(
      username: json['username'] as String,
      password: json['password'] as String,
      createdAt: json['created_at'] as String?,
    );

Map<String, dynamic> _$AccountToJson(Account instance) => <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
      'created_at': instance.createdAt,
    };
