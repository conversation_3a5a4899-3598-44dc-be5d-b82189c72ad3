{"index": "bard-expertise-1", "class": {"index": "bard", "name": "Bard", "url": "/api/2014/classes/bard"}, "name": "Expertise", "level": 3, "prerequisites": [], "desc": ["At 3rd level, choose two of your skill proficiencies. Your proficiency bonus is doubled for any ability check you make that uses either of the chosen proficiencies. At 10th level, you can choose another two skill proficiencies to gain this benefit."], "feature_specific": {"expertise_options": {"choose": 2, "type": "proficiency", "from": {"option_set_type": "options_array", "options": [{"option_type": "reference", "item": {"index": "skill-acrobatics", "name": "Skill: Acrobatics", "url": "/api/2014/proficiencies/skill-acrobatics"}}, {"option_type": "reference", "item": {"index": "skill-animal-handling", "name": "Skill: Animal Handling", "url": "/api/2014/proficiencies/skill-animal-handling"}}, {"option_type": "reference", "item": {"index": "skill-arcana", "name": "Skill: <PERSON>ana", "url": "/api/2014/proficiencies/skill-arcana"}}, {"option_type": "reference", "item": {"index": "skill-athletics", "name": "Skill: Athletics", "url": "/api/2014/proficiencies/skill-athletics"}}, {"option_type": "reference", "item": {"index": "skill-deception", "name": "Skill: Deception", "url": "/api/2014/proficiencies/skill-deception"}}, {"option_type": "reference", "item": {"index": "skill-history", "name": "Skill: History", "url": "/api/2014/proficiencies/skill-history"}}, {"option_type": "reference", "item": {"index": "skill-insight", "name": "Skill: Insight", "url": "/api/2014/proficiencies/skill-insight"}}, {"option_type": "reference", "item": {"index": "skill-intimidation", "name": "Skill: Intimidation", "url": "/api/2014/proficiencies/skill-intimidation"}}, {"option_type": "reference", "item": {"index": "skill-investigation", "name": "Skill: Investigation", "url": "/api/2014/proficiencies/skill-investigation"}}, {"option_type": "reference", "item": {"index": "skill-medicine", "name": "Skill: Medicine", "url": "/api/2014/proficiencies/skill-medicine"}}, {"option_type": "reference", "item": {"index": "skill-nature", "name": "Skill: Nature", "url": "/api/2014/proficiencies/skill-nature"}}, {"option_type": "reference", "item": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"option_type": "reference", "item": {"index": "skill-performance", "name": "Skill: Performance", "url": "/api/2014/proficiencies/skill-performance"}}, {"option_type": "reference", "item": {"index": "skill-persuasion", "name": "Skill: Per<PERSON><PERSON><PERSON>", "url": "/api/2014/proficiencies/skill-persuasion"}}, {"option_type": "reference", "item": {"index": "skill-religion", "name": "Skill: Religion", "url": "/api/2014/proficiencies/skill-religion"}}, {"option_type": "reference", "item": {"index": "skill-sleight-of-hand", "name": "Skill: Sleight of Hand", "url": "/api/2014/proficiencies/skill-sleight-of-hand"}}, {"option_type": "reference", "item": {"index": "skill-stealth", "name": "Skill: <PERSON>eal<PERSON>", "url": "/api/2014/proficiencies/skill-stealth"}}, {"option_type": "reference", "item": {"index": "skill-survival", "name": "Skill: Survival", "url": "/api/2014/proficiencies/skill-survival"}}]}}, "invocations": []}, "url": "/api/2014/features/bard-expertise-1", "updated_at": "2025-04-08T21:14:01.855Z"}