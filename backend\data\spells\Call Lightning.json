{"index": "call-lightning", "name": "Call Lightning", "desc": ["A storm cloud appears in the shape of a cylinder that is 10 feet tall with a 60-foot radius, centered on a point you can see 100 feet directly above you. The spell fails if you can't see a point in the air where the storm cloud could appear (for example, if you are in a room that can't accommodate the cloud).", "When you cast the spell, choose a point you can see within range. A bolt of lightning flashes down from the cloud to that point. Each creature within 5 feet of that point must make a dexterity saving throw. A creature takes 3d10 lightning damage on a failed save, or half as much damage on a successful one. On each of your turns until the spell ends, you can use your action to call down lightning in this way again, targeting the same point or a different one.", "If you are outdoors in stormy conditions when you cast this spell, the spell gives you control over the existing storm instead of creating a new one. Under such conditions, the spell's damage increases by 1d10."], "higher_level": ["When you cast this spell using a spell slot of 4th or higher level, the damage increases by 1d10 for each slot level above 3rd."], "range": "120 feet", "components": ["V", "S"], "ritual": false, "duration": "Up to 10 minutes", "concentration": true, "casting_time": "1 action", "level": 3, "damage": {"damage_type": {"index": "lightning", "name": "Lightning", "url": "/api/2014/damage-types/lightning"}, "damage_at_slot_level": {"3": "3d10", "4": "4d10", "5": "5d10", "6": "6d10", "7": "7d10", "8": "8d10", "9": "9d10"}}, "area_of_effect": {"type": "sphere", "size": 5}, "school": {"index": "conjuration", "name": "Conjuration", "url": "/api/2014/magic-schools/conjuration"}, "classes": [{"index": "druid", "name": "Druid", "url": "/api/2014/classes/druid"}], "subclasses": [{"index": "lore", "name": "Lore", "url": "/api/2014/subclasses/lore"}, {"index": "land", "name": "Land", "url": "/api/2014/subclasses/land"}], "url": "/api/2014/spells/call-lightning", "updated_at": "2025-04-08T21:14:16.147Z"}