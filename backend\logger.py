"""
统一的日志系统模块
提供全局日志功能，支持文件和控制台输出
"""
import os
import sys
import logging
import traceback
import inspect
from pathlib import Path
from logging.handlers import RotatingFileHandler

from config import (
    LOGS_DIR, 
    LOG_LEVEL, 
    LOG_FILE, 
    LOG_FORMAT, 
    LOG_DATE_FORMAT, 
    LOG_TO_CONSOLE,
    BASE_DIR
)

# 自定义日志器，支持记录调用者信息
class CallerInfoLogger(logging.Logger):
    def _log(self, level, msg, args, exc_info=None, extra=None, stack_info=False, stacklevel=1):
        # 获取调用者信息
        frame = inspect.currentframe()
        # 向上遍历调用栈直到找到非logger.py的文件
        for i in range(3):  # 跳过当前帧和一些内部调用
            if frame:
                frame = frame.f_back
        
        # 如果找到非logger.py的调用者
        if frame:
            # 获取完整的文件路径和行号
            filepath = frame.f_code.co_filename
            filename = os.path.basename(filepath)
            lineno = frame.f_lineno
            
            # 检查文件是否属于当前项目
            if 'site-packages' not in filepath:
                # 如果是项目内的文件，尝试获取相对路径
                try:
                    relpath = os.path.relpath(filepath, start=str(BASE_DIR))
                    if not relpath.startswith('..'):
                        filename = relpath
                except:
                    pass
            
            # 创建或更新extra
            if extra is None:
                extra = {}
            extra.update({
                'custom_filename': filename,
                'custom_lineno': lineno
            })
        
        # 调用父类方法
        super()._log(level, msg, args, exc_info, extra, stack_info, stacklevel)

# 自定义格式化器，使用自定义文件名和行号
class CallerInfoFormatter(logging.Formatter):
    def format(self, record):
        # 使用自定义文件名和行号（如果可用）
        if hasattr(record, 'custom_filename'):
            record.filename = record.custom_filename
        if hasattr(record, 'custom_lineno'):
            record.lineno = record.custom_lineno
        
        return super().format(record)

def setup_logger():
    """
    设置并返回全局日志记录器
    """
    # 注册自定义日志器类
    logging.setLoggerClass(CallerInfoLogger)
    
    # 确保日志目录存在
    os.makedirs(LOGS_DIR, exist_ok=True)
    
    # 创建日志记录器
    logger = logging.getLogger("dnd_game")
    
    # 设置日志级别
    level = getattr(logging, LOG_LEVEL.upper(), logging.DEBUG)
    logger.setLevel(level)
    
    # 防止日志重复
    if logger.handlers:
        return logger
    
    # 创建自定义格式化器
    formatter = CallerInfoFormatter(LOG_FORMAT, LOG_DATE_FORMAT)
    
    # 创建文件处理器 (使用RotatingFileHandler限制文件大小)
    file_handler = RotatingFileHandler(
        LOG_FILE,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 如果配置了控制台输出，添加控制台处理器
    if LOG_TO_CONSOLE:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger

# 创建全局日志实例
logger = setup_logger()

# 提供便捷的日志记录函数
def debug(msg, *args, **kwargs):
    logger.debug(msg, *args, **kwargs)

def info(msg, *args, **kwargs):
    logger.info(msg, *args, **kwargs)

def warning(msg, *args, **kwargs):
    logger.warning(msg, *args, **kwargs)

def error(msg, *args, **kwargs):
    logger.error(msg, *args, **kwargs)

def critical(msg, *args, **kwargs):
    logger.critical(msg, *args, **kwargs)

def exception(msg, *args, **kwargs):
    """
    记录异常信息，包括堆栈跟踪
    """
    logger.exception(msg, *args, **kwargs)

# 设置全局未处理异常处理器
def handle_uncaught_exception(exc_type, exc_value, exc_traceback):
    """
    处理全局未捕获的异常
    """
    if issubclass(exc_type, KeyboardInterrupt):
        # 不处理键盘中断
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # 构建详细错误信息
    error_msg = f"未捕获的异常: {exc_type.__name__}: {exc_value}"
    error_details = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    
    # 记录到日志
    critical(error_msg)
    critical(f"详细堆栈: \n{error_details}")
    
    # 调用原始的异常处理器
    sys.__excepthook__(exc_type, exc_value, exc_traceback)

# 设置全局异常处理器
sys.excepthook = handle_uncaught_exception

# 记录启动信息
info("日志系统已初始化") 