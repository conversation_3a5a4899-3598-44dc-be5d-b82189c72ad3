// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'character.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BasicInfo _$BasicInfoFromJson(Map<String, dynamic> json) => BasicInfo(
      name: json['name'] as String,
      player: json['player'] as String,
      race: json['race'] as String,
      characterClass: json['class'] as String,
      level: json['level'] as int,
      exp: json['exp'] as int,
      age: json['age'] as int,
      gender: json['gender'] as String,
    );

Map<String, dynamic> _$BasicInfoToJson(BasicInfo instance) => <String, dynamic>{
      'name': instance.name,
      'player': instance.player,
      'race': instance.race,
      'class': instance.characterClass,
      'level': instance.level,
      'exp': instance.exp,
      'age': instance.age,
      'gender': instance.gender,
    };

CharacterSummary _$CharacterSummaryFromJson(Map<String, dynamic> json) =>
    CharacterSummary(
      id: json['id'] as String,
      basic: BasicInfo.fromJson(json['basic'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CharacterSummaryToJson(CharacterSummary instance) =>
    <String, dynamic>{
      'id': instance.id,
      'basic': instance.basic,
    };

CharacterAttributes _$CharacterAttributesFromJson(Map<String, dynamic> json) =>
    CharacterAttributes(
      strength: json['STR'] as int,
      dexterity: json['DEX'] as int,
      mind: json['MIND'] as int,
    );

Map<String, dynamic> _$CharacterAttributesToJson(
        CharacterAttributes instance) =>
    <String, dynamic>{
      'STR': instance.strength,
      'DEX': instance.dexterity,
      'MIND': instance.mind,
    };

DerivedStats _$DerivedStatsFromJson(Map<String, dynamic> json) => DerivedStats(
      hp: json['hp'] as int,
      hpBreakdown:
          HpBreakdown.fromJson(json['hp_breakdown'] as Map<String, dynamic>),
      ac: json['ac'] as int,
      acBreakdown:
          AcBreakdown.fromJson(json['ac_breakdown'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DerivedStatsToJson(DerivedStats instance) =>
    <String, dynamic>{
      'hp': instance.hp,
      'hp_breakdown': instance.hpBreakdown,
      'ac': instance.ac,
      'ac_breakdown': instance.acBreakdown,
    };

HpBreakdown _$HpBreakdownFromJson(Map<String, dynamic> json) => HpBreakdown(
      base: json['base'] as int,
      bonus: json['bonus'] as int,
    );

Map<String, dynamic> _$HpBreakdownToJson(HpBreakdown instance) =>
    <String, dynamic>{
      'base': instance.base,
      'bonus': instance.bonus,
    };

AcBreakdown _$AcBreakdownFromJson(Map<String, dynamic> json) => AcBreakdown(
      base: json['base'] as int,
      dex: json['dex'] as int,
      armor: json['armor'] as int,
      shield: json['shield'] as int,
      misc: json['misc'] as int,
    );

Map<String, dynamic> _$AcBreakdownToJson(AcBreakdown instance) =>
    <String, dynamic>{
      'base': instance.base,
      'dex': instance.dex,
      'armor': instance.armor,
      'shield': instance.shield,
      'misc': instance.misc,
    };

Skills _$SkillsFromJson(Map<String, dynamic> json) => Skills(
      physical:
          SkillDetail.fromJson(json['Physical'] as Map<String, dynamic>),
      subterfuge:
          SkillDetail.fromJson(json['Subterfuge'] as Map<String, dynamic>),
      knowledge:
          SkillDetail.fromJson(json['Knowledge'] as Map<String, dynamic>),
      communication:
          SkillDetail.fromJson(json['Communication'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SkillsToJson(Skills instance) => <String, dynamic>{
      'Physical': instance.physical,
      'Subterfuge': instance.subterfuge,
      'Knowledge': instance.knowledge,
      'Communication': instance.communication,
    };

SkillDetail _$SkillDetailFromJson(Map<String, dynamic> json) => SkillDetail(
      base: json['base'] as int,
      classBonus: json['class_bonus'] as int,
      raceBonus: json['race_bonus'] as int,
    );

Map<String, dynamic> _$SkillDetailToJson(SkillDetail instance) =>
    <String, dynamic>{
      'base': instance.base,
      'class_bonus': instance.classBonus,
      'race_bonus': instance.raceBonus,
    };

CreateCharacterRequest _$CreateCharacterRequestFromJson(
        Map<String, dynamic> json) =>
    CreateCharacterRequest(
      username: json['username'] as String,
      characterName: json['character_name'] as String,
      race: json['race'] as String,
      characterClass: json['character_class'] as String,
      gender: json['gender'] as String,
      age: json['age'] as int,
      attributes: CharacterAttributes.fromJson(
          json['attributes'] as Map<String, dynamic>),
      derivedStats: DerivedStats.fromJson(
          json['derived_stats'] as Map<String, dynamic>),
      skills: Skills.fromJson(json['skills'] as Map<String, dynamic>),
      currency: json['currency'] as int,
    );

Map<String, dynamic> _$CreateCharacterRequestToJson(
        CreateCharacterRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'character_name': instance.characterName,
      'race': instance.race,
      'character_class': instance.characterClass,
      'gender': instance.gender,
      'age': instance.age,
      'attributes': instance.attributes,
      'derived_stats': instance.derivedStats,
      'skills': instance.skills,
      'currency': instance.currency,
    };

DeleteCharacterRequest _$DeleteCharacterRequestFromJson(
        Map<String, dynamic> json) =>
    DeleteCharacterRequest(
      username: json['username'] as String,
      characterId: json['character_id'] as String,
    );

Map<String, dynamic> _$DeleteCharacterRequestToJson(
        DeleteCharacterRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'character_id': instance.characterId,
    };
