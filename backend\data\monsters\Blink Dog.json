{"index": "blink-dog", "name": "Blink Dog", "desc": "A blink dog takes its name from its ability to blink in and out of existence, a talent it uses to aid its attacks and to avoid harm.", "size": "Medium", "type": "fey", "alignment": "lawful good", "armor_class": [{"type": "dex", "value": 13}], "hit_points": 22, "hit_dice": "4d8", "hit_points_roll": "4d8+4", "speed": {"walk": "40 ft."}, "strength": 12, "dexterity": 17, "constitution": 12, "intelligence": 10, "wisdom": 13, "charisma": 11, "proficiencies": [{"value": 3, "proficiency": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"value": 5, "proficiency": {"index": "skill-stealth", "name": "Skill: <PERSON>eal<PERSON>", "url": "/api/2014/proficiencies/skill-stealth"}}], "damage_vulnerabilities": [], "damage_resistances": [], "damage_immunities": [], "condition_immunities": [], "senses": {"passive_perception": 10}, "languages": "<PERSON><PERSON> <PERSON>, understands <PERSON><PERSON><PERSON> but can't speak it", "challenge_rating": 0.25, "proficiency_bonus": 2, "xp": 50, "special_abilities": [{"name": "Keen Hearing and Smell", "desc": "The dog has advantage on Wisdom (Perception) checks that rely on hearing or smell.", "damage": []}], "actions": [{"name": "Bite", "desc": "Melee Weapon Attack: +3 to hit, reach 5 ft., one target. Hit: 4 (1d6 + 1) piercing damage.", "attack_bonus": 3, "damage": [{"damage_type": {"index": "piercing", "name": "Piercing", "url": "/api/2014/damage-types/piercing"}, "damage_dice": "1d6+1"}], "actions": []}, {"name": "Teleport", "desc": "The dog magically teleports, along with any equipment it is wearing or carrying, up to 40 ft. to an unoccupied space it can see. Before or after teleporting, the dog can make one bite attack.", "usage": {"type": "recharge on roll", "dice": "1d6", "min_value": 4}, "damage": [], "actions": []}], "image": "/api/images/monsters/blink-dog.png", "url": "/api/2014/monsters/blink-dog", "updated_at": "2025-05-04T02:15:02.222Z", "forms": [], "legendary_actions": [], "reactions": []}