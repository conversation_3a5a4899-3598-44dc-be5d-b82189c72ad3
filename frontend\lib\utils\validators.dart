/// 表单验证器
class Validators {
  /// 验证用户名
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入用户名';
    }
    if (value.length < 3) {
      return '用户名至少需要3个字符';
    }
    if (value.length > 20) {
      return '用户名不能超过20个字符';
    }
    // 只允许字母、数字和下划线
    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
      return '用户名只能包含字母、数字和下划线';
    }
    return null;
  }
  
  /// 验证密码
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    if (value.length < 6) {
      return '密码至少需要6个字符';
    }
    return null;
  }
  
  /// 验证角色名
  static String? validateCharacterName(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入角色名';
    }
    if (value.length < 2) {
      return '角色名至少需要2个字符';
    }
    if (value.length > 15) {
      return '角色名不能超过15个字符';
    }
    return null;
  }
  
  /// 验证年龄
  static String? validateAge(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入年龄';
    }
    final age = int.tryParse(value);
    if (age == null) {
      return '请输入有效的年龄';
    }
    if (age < 16 || age > 100) {
      return '年龄必须在16-100之间';
    }
    return null;
  }
  
  /// 验证属性值
  static String? validateAttribute(String? value, {int min = 8, int max = 18}) {
    if (value == null || value.isEmpty) {
      return '请输入属性值';
    }
    final attr = int.tryParse(value);
    if (attr == null) {
      return '请输入有效的属性值';
    }
    if (attr < min || attr > max) {
      return '属性值必须在$min-$max之间';
    }
    return null;
  }
}
