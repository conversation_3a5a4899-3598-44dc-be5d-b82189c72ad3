import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/character_provider.dart';
import '../widgets/custom_button.dart';
import 'character_selection_screen.dart';

/// 游戏主界面（占位符）
class GameScreen extends StatelessWidget {
  const GameScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('DND 游戏'),
        actions: [
          IconButton(
            onPressed: () => _returnToCharacterSelection(context),
            icon: const Icon(Icons.arrow_back),
            tooltip: '返回角色选择',
          ),
        ],
      ),
      body: Consumer2<AuthProvider, CharacterProvider>(
        builder: (context, authProvider, characterProvider, child) {
          final character = characterProvider.selectedCharacter;
          
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 角色信息卡片
                if (character != null) ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '当前角色',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: _getRaceColor(character.race),
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                child: Icon(
                                  _getClassIcon(character.characterClass),
                                  color: Colors.white,
                                  size: 30,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      character.name,
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      '${character.race} ${character.characterClass}',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    Text(
                                      '等级 ${character.level}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // 游戏功能区域
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.construction,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '游戏功能开发中',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '广场、副本、武器商店等功能即将上线',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 32),
                        
                        // 功能按钮（占位符）
                        Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          children: [
                            _buildFeatureButton(
                              context,
                              '广场',
                              Icons.location_city,
                              Colors.blue,
                              () => _showComingSoon(context, '广场'),
                            ),
                            _buildFeatureButton(
                              context,
                              '传送点',
                              Icons.explore,
                              Colors.green,
                              () => _showComingSoon(context, '传送点'),
                            ),
                            _buildFeatureButton(
                              context,
                              '武器商店',
                              Icons.store,
                              Colors.orange,
                              () => _showComingSoon(context, '武器商店'),
                            ),
                            _buildFeatureButton(
                              context,
                              '副本',
                              Icons.castle,
                              Colors.red,
                              () => _showComingSoon(context, '副本'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // 返回按钮
                CustomButton(
                  text: '返回角色选择',
                  onPressed: () => _returnToCharacterSelection(context),
                  width: double.infinity,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建功能按钮
  Widget _buildFeatureButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      width: 120,
      height: 100,
      child: Card(
        elevation: 4,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 32, color: color),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 返回角色选择界面
  void _returnToCharacterSelection(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const CharacterSelectionScreen(),
      ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(feature),
        content: Text('$feature功能即将推出，敬请期待！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 根据种族获取颜色
  Color _getRaceColor(String race) {
    switch (race) {
      case '人类':
        return Colors.blue;
      case '精灵':
        return Colors.green;
      case '矮人':
        return Colors.brown;
      case '半身人':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// 根据职业获取图标
  IconData _getClassIcon(String characterClass) {
    switch (characterClass) {
      case '战士':
        return Icons.shield;
      case '法师':
        return Icons.auto_fix_high;
      case '盗贼':
        return Icons.visibility_off;
      case '牧师':
        return Icons.healing;
      default:
        return Icons.person;
    }
  }
}
