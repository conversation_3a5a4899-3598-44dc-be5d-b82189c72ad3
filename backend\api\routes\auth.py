"""
认证相关路由
"""
from fastapi import APIRouter, HTTPException

from models import LoginRequest, ApiResponse
from auth_services.auth_service import AuthService
from logger import debug, info, warning, error

router = APIRouter(prefix="/auth", tags=["认证"])

@router.post("/login", response_model=ApiResponse)
async def login(request: LoginRequest):
    """
    用户登录
    """
    debug(f"尝试登录用户: {request.username}")
    
    success, message, data = AuthService.login(request.username, request.password)
    
    if not success:
        warning(f"登录失败: {request.username}, 原因: {message}")
        raise HTTPException(status_code=401, detail=message)
    
    info(f"用户 {request.username} 登录成功")
    return ApiResponse(success=success, message=message, data=data) 