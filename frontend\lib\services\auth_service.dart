import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_response.dart';
import '../models/user.dart';
import '../models/character.dart';
import '../utils/api_config.dart';
import '../utils/constants.dart';
import '../utils/debug_logger.dart';
import 'api_service.dart';

/// 认证服务类
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();

  /// 用户登录
  Future<User> login(String username, String password) async {
    DebugLogger.info('开始用户登录流程', tag: 'AUTH_SERVICE');
    DebugLogger.debug('登录用户名: $username', tag: 'AUTH_SERVICE');
    DebugLogger.debug('登录端点: ${ApiConfig.loginEndpoint}', tag: 'AUTH_SERVICE');

    final loginRequest = LoginRequest(
      username: username,
      password: password,
    );

    DebugLogger.debug('发送登录请求', tag: 'AUTH_SERVICE');
    final response = await _apiService.post(
      ApiConfig.loginEndpoint,
      loginRequest.toJson(),
    );

    DebugLogger.debug('收到登录响应，开始解析', tag: 'AUTH_SERVICE');
    DebugLogger.debug('原始响应数据: $response', tag: 'AUTH_SERVICE');

    try {
      // 解析响应
      final apiResponse = ApiResponse<LoginResponseData>.fromJson(
        response,
        (json) {
          DebugLogger.debug('解析LoginResponseData: $json', tag: 'AUTH_SERVICE');
          return LoginResponseData.fromJson(json as Map<String, dynamic>);
        },
      );

      DebugLogger.debug('API响应解析完成: success=${apiResponse.success}', tag: 'AUTH_SERVICE');
      if (!apiResponse.success || apiResponse.data == null) {
        DebugLogger.error('登录失败: ${apiResponse.message}', tag: 'AUTH_SERVICE');
        throw Exception(apiResponse.message);
      }

      final loginData = apiResponse.data!;
      DebugLogger.info('登录成功，用户: ${loginData.username}', tag: 'AUTH_SERVICE');
      DebugLogger.debug('角色数量: ${loginData.characters.length}', tag: 'AUTH_SERVICE');

      final user = User(
        username: loginData.username,
        characters: loginData.characters,
      );

      // 保存登录信息到本地
      DebugLogger.debug('保存登录信息到本地存储', tag: 'AUTH_SERVICE');
      await _saveLoginInfo(username);

      return user;
    } catch (e, stackTrace) {
      DebugLogger.error('登录解析过程中发生异常: $e', tag: 'AUTH_SERVICE');
      DebugLogger.debug('异常堆栈: $stackTrace', tag: 'AUTH_SERVICE');
      throw Exception('登录解析失败: $e');
    }
  }

  /// 保存登录信息到本地存储
  Future<void> _saveLoginInfo(String username) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.usernameKey, username);
    await prefs.setBool('is_logged_in', true);
  }

  /// 获取本地保存的用户名
  Future<String?> getSavedUsername() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.usernameKey);
  }

  /// 检查是否已登录
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('is_logged_in') ?? false;
  }

  /// 用户登出
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.usernameKey);
    await prefs.remove('is_logged_in');
    await prefs.remove(AppConstants.lastCharacterIdKey);
  }

  /// 保存最后选择的角色ID
  Future<void> saveLastCharacterId(String characterId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.lastCharacterIdKey, characterId);
  }

  /// 获取最后选择的角色ID
  Future<String?> getLastCharacterId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.lastCharacterIdKey);
  }
}
