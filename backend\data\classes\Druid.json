{"index": "druid", "name": "Druid", "hit_die": 8, "proficiency_choices": [{"desc": "Choose two from Arcana, Animal Handling, Insight, Medicine, Nature, Perception, Religion, and Survival", "choose": 2, "type": "proficiencies", "from": {"option_set_type": "options_array", "options": [{"option_type": "reference", "item": {"index": "skill-arcana", "name": "Skill: <PERSON>ana", "url": "/api/2014/proficiencies/skill-arcana"}}, {"option_type": "reference", "item": {"index": "skill-animal-handling", "name": "Skill: Animal Handling", "url": "/api/2014/proficiencies/skill-animal-handling"}}, {"option_type": "reference", "item": {"index": "skill-insight", "name": "Skill: Insight", "url": "/api/2014/proficiencies/skill-insight"}}, {"option_type": "reference", "item": {"index": "skill-medicine", "name": "Skill: Medicine", "url": "/api/2014/proficiencies/skill-medicine"}}, {"option_type": "reference", "item": {"index": "skill-nature", "name": "Skill: Nature", "url": "/api/2014/proficiencies/skill-nature"}}, {"option_type": "reference", "item": {"index": "skill-perception", "name": "Skill: Perception", "url": "/api/2014/proficiencies/skill-perception"}}, {"option_type": "reference", "item": {"index": "skill-religion", "name": "Skill: Religion", "url": "/api/2014/proficiencies/skill-religion"}}, {"option_type": "reference", "item": {"index": "skill-survival", "name": "Skill: Survival", "url": "/api/2014/proficiencies/skill-survival"}}]}}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "medium-armor", "name": "Medium Armor", "url": "/api/2014/proficiencies/medium-armor"}, {"index": "shields", "name": "Shields", "url": "/api/2014/proficiencies/shields"}, {"index": "clubs", "name": "Clubs", "url": "/api/2014/proficiencies/clubs"}, {"index": "daggers", "name": "Daggers", "url": "/api/2014/proficiencies/daggers"}, {"index": "javelins", "name": "Javelins", "url": "/api/2014/proficiencies/javelins"}, {"index": "maces", "name": "Maces", "url": "/api/2014/proficiencies/maces"}, {"index": "quarterstaffs", "name": "Quarterstaffs", "url": "/api/2014/proficiencies/quarterstaffs"}, {"index": "sickles", "name": "<PERSON><PERSON>", "url": "/api/2014/proficiencies/sickles"}, {"index": "spears", "name": "<PERSON>", "url": "/api/2014/proficiencies/spears"}, {"index": "darts", "name": "Darts", "url": "/api/2014/proficiencies/darts"}, {"index": "slings", "name": "Slings", "url": "/api/2014/proficiencies/slings"}, {"index": "scimitars", "name": "Scimitars", "url": "/api/2014/proficiencies/scimitars"}, {"index": "herbalism-kit", "name": "Herbalism Kit", "url": "/api/2014/proficiencies/herbalism-kit"}, {"index": "saving-throw-int", "name": "Saving Throw: INT", "url": "/api/2014/proficiencies/saving-throw-int"}, {"index": "saving-throw-wis", "name": "Saving Throw: WIS", "url": "/api/2014/proficiencies/saving-throw-wis"}], "saving_throws": [{"index": "int", "name": "INT", "url": "/api/2014/ability-scores/int"}, {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}], "starting_equipment": [{"equipment": {"index": "leather-armor", "name": "<PERSON><PERSON> Armor", "url": "/api/2014/equipment/leather-armor"}, "quantity": 1}, {"equipment": {"index": "explorers-pack", "name": "Explorer's Pack", "url": "/api/2014/equipment/explorers-pack"}, "quantity": 1}], "starting_equipment_options": [{"desc": "(a) a wooden shield or (b) any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "shield", "name": "Shield", "url": "/api/2014/equipment/shield"}}, {"option_type": "choice", "choice": {"desc": "any simple weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "simple-weapons", "name": "Simple Weapons", "url": "/api/2014/equipment-categories/simple-weapons"}}}}]}}, {"desc": "(a) a scimitar or (b) any simple melee weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "options_array", "options": [{"option_type": "counted_reference", "count": 1, "of": {"index": "scimitar", "name": "Scimitar", "url": "/api/2014/equipment/scimitar"}}, {"option_type": "choice", "choice": {"desc": "any simple melee weapon", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "simple-melee-weapons", "name": "Simple Melee Weapons", "url": "/api/2014/equipment-categories/simple-melee-weapons"}}}}]}}, {"desc": "druidic focus", "choose": 1, "type": "equipment", "from": {"option_set_type": "equipment_category", "equipment_category": {"index": "druidic-foci", "name": "<PERSON><PERSON><PERSON>", "url": "/api/2014/equipment-categories/druidic-foci"}}}], "class_levels": "/api/2014/classes/druid/levels", "multi_classing": {"prerequisites": [{"ability_score": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "minimum_score": 13}], "proficiencies": [{"index": "light-armor", "name": "Light Armor", "url": "/api/2014/proficiencies/light-armor"}, {"index": "medium-armor", "name": "Medium Armor", "url": "/api/2014/proficiencies/medium-armor"}, {"index": "shields", "name": "Shields", "url": "/api/2014/proficiencies/shields"}]}, "subclasses": [{"index": "land", "name": "Land", "url": "/api/2014/subclasses/land"}], "spellcasting": {"level": 1, "spellcasting_ability": {"index": "wis", "name": "WIS", "url": "/api/2014/ability-scores/wis"}, "info": [{"name": "Cantrips", "desc": ["At 1st level, you know two cantrips of your choice from the druid spell list. You learn additional druid cantrips of your choice at higher levels, as shown in the Cantrips Known column of the Druid table."]}, {"name": "Preparing and Casting Spells", "desc": ["The Druid table shows how many spell slots you have to cast your spells of 1st level and higher. To cast one of these druid spells, you must expend a slot of the spell's level or higher. You regain all expended spell slots when you finish a long rest.", "You prepare the list of druid spells that are available for you to cast, choosing from the druid spell list. When you do so, choose a number of druid spells equal to your Wisdom modifier + your druid level (minimum of one spell). The spells must be of a level for which you have spell slots.", "For example, if you are a 3rd-level druid, you have four 1st-level and two 2nd-level spell slots. With a Wisdom of 16, your list of prepared spells can include six spells of 1st or 2nd level, in any combination. If you prepare the 1st-level spell cure wounds, you can cast it using a 1st-level or 2nd-level slot. Casting the spell doesn't remove it from your list of prepared spells.", "You can also change your list of prepared spells when you finish a long rest. Preparing a new list of druid spells requires time spent in prayer and meditation: at least 1 minute per spell level for each spell on your list."]}, {"name": "Spellcasting Ability", "desc": ["Wisdom is your spellcasting ability for your druid spells, since your magic draws upon your devotion and attunement to nature. You use your Wisdom whenever a spell refers to your spellcasting ability. In addition, you use your Wisdom modifier when setting the saving throw DC for a druid spell you cast and when making an attack roll with one.", "Spell save DC = 8 + your proficiency bonus + your Wisdom modifier.", "Spell attack modifier = your proficiency bonus + your Wisdom modifier."]}, {"name": "Ritual Casting", "desc": ["You can cast a druid spell as a ritual if that spell has the ritual tag and you have the spell prepared."]}, {"name": "Spellcasting Focus", "desc": ["You can use a druidic focus (see chapter 5, \"Equipment\") as a spellcasting focus for your druid spells."]}]}, "spells": "/api/2014/classes/druid/spells", "url": "/api/2014/classes/druid", "updated_at": "2025-04-08T21:13:56.155Z"}