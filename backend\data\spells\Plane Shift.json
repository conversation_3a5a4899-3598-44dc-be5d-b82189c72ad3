{"higher_level": [], "index": "plane-shift", "name": "Plane Shift", "desc": ["You and up to eight willing creatures who link hands in a circle are transported to a different plane of existence. You can specify a target destination in general terms, such as the City of Brass on the Elemental Plane of Fire or the palace of Dispater on the second level of the Nine Hells, and you appear in or near that destination. If you are trying to reach the City of Brass, for example, you might arrive in its Street of Steel, before its Gate of Ashes, or looking at the city from across the Sea of Fire, at the GM's discretion.", "Alternatively, if you know the sigil sequence of a teleportation circle on another plane of existence, this spell can take you to that circle. If the teleportation circle is too small to hold all the creatures you transported, they appear in the closest unoccupied spaces next to the circle.", "You can use this spell to banish an unwilling creature to another plane. Choose a creature within your reach and make a melee spell attack against it. On a hit, the creature must make a charisma saving throw. If the creature fails this save, it is transported to a random location on the plane of existence you specify. A creature so transported must find its own way back to your current plane of existence."], "range": "Touch", "components": ["V", "S", "M"], "material": "A forked, metal rod worth at least 250 gp, attuned to a particular plane of existence.", "ritual": false, "duration": "Instantaneous", "concentration": false, "casting_time": "1 action", "level": 7, "attack_type": "melee", "dc": {"dc_type": {"index": "cha", "name": "CHA", "url": "/api/2014/ability-scores/cha"}, "dc_success": "none", "desc": "If the creature fails this save, it is transported to a random location on the plane of existence you specify. A creature so transported must find its own way back to your current plane of existence."}, "school": {"index": "conjuration", "name": "Conjuration", "url": "/api/2014/magic-schools/conjuration"}, "classes": [{"index": "cleric", "name": "Cleric", "url": "/api/2014/classes/cleric"}, {"index": "druid", "name": "Druid", "url": "/api/2014/classes/druid"}, {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}, {"index": "warlock", "name": "<PERSON><PERSON>", "url": "/api/2014/classes/warlock"}, {"index": "wizard", "name": "<PERSON>", "url": "/api/2014/classes/wizard"}], "subclasses": [], "url": "/api/2014/spells/plane-shift", "updated_at": "2025-04-08T21:14:16.147Z"}