"""
项目运行脚本 - 仅启动API服务器
"""
import os
import sys

def run_server():
    """运行API服务器"""
    from config import HOST, PORT
    from main import app
    from logger import info, error, critical
    import uvicorn

    info("启动DND游戏API服务器...")
    info(f"服务器将运行在: http://{HOST}:{PORT}")
    info("API文档地址: http://localhost:8000/docs")
    info("前端请使用Flutter应用连接到此API服务器")

    # 运行服务器
    uvicorn.run(app, host=HOST, port=PORT)

def main():
    """主函数"""
    # 导入日志模块，确保日志系统初始化
    from logger import info, error, critical

    info("=== 启动DND游戏API服务器 ===")

    try:
        # 运行API服务器
        run_server()
    except KeyboardInterrupt:
        info("\nAPI服务器被用户中断，正在停止...")
        sys.exit(0)
    except Exception as e:
        critical(f"API服务器启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 